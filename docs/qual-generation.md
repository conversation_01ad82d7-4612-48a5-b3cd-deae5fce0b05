
# Qual generation
## Chatbot
![Chat flow](images/Architecture-Chat-flow.drawio.png)

###  Intent classification
As part of user prompt processing, we will classify the user prompt into one of the following intents:

**Generate Qual**: When user wants to generate a new Qual, all data was provided in the prompt.

**Extract data**: When user provides a prompt with some data and wants to extract data from the prompt.

**Example help**: When user wants to see example of the prompt.

**Dash: show more**: When user wants to see more dash tasks, except those that are already shown in the prompt.

**Dash: discard**: When user wants to discard selecting dash task and instead wants to provide data in prompts.

**Undefined**: When user prompt does not match any of the above intents.

Intent classification will be done using LLM + Structured output.
As system prompt, we will provide instructions to select one of the intents from the list above. We also provide examples of the intents and how to classify them. Along with that, we will provide conversation history.

Response will be in the following format:

```json
{
    "intent": "<intent_string>", // Intent name from the list above.
}
```


### Extract data from the user prompt
Data extraction will be done using LLM + Structured output.

We will ask LLM to extract data that was not extracted before. Schema of the output should be adjusted according to the data that was not extracted before. For example, if user provided client name and lead member firm, we will ask LLM to extract engagement dates, objective and scope and outcomes.

As part of system prompt we will also provide member firms list along with ids.

Structured output will be in the following format:
```json
{
    "client": {
        "name": "<client_name_string>",
        "multiple_names_detected": "<multiple_names_detected_boolean>", // True if multiple names were detected in the prompt.
        "names": [ // List of client names detected in the prompt if multiple names detected
            "<client_name_string>"
        ],
    },
    "lead_member_firm": {
        "id": "<lead_member_firm_id_int>", // Member firm provided dictionary.
        "name": "<lead_member_firm_name_string>", // Member firm name.
        "multiple_member_firms_detected": "<multiple_member_firms_detected_boolean>", // True if multiple member firms were detected in the prompt.
        "member_firms": [ // List of member firms detected in the prompt if multiple member firms detected or one ambiguous.
            {
                "id": "<lead_member_firm_id_int>", // Member firm id from KX Qual API.
                "name": "<lead_member_firm_name_string>" // Member firm name.
            }
        ]
    },
    "engagement_dates": {
        "start_date": {
            "date": "<start_date_string>", // Engagement start date in MM/DD/YYYY format.
            "multiple_dates_detected": "<multiple_dates_detected_boolean>", // True if multiple dates were detected in the prompt.
            "dates": [ // List of dates detected in the prompt if multiple dates detected
                "<start_date_string>"
            ]
        },
        "end_date": {
            "date": "<end_date_string>", // Engagement end date in MM/DD/YYYY format.
            "multiple_dates_detected": "<multiple_dates_detected_boolean>", // True if multiple dates were detected in the prompt.
            "dates": [ // List of dates detected in the prompt if multiple dates detected
                "<end_date_string>"
            ]
        }
    },
    "objective_and_scope": "<objective_and_scope_string>", // Engagement objective and scope.,
    "outcomes": "<outcomes_string>" // Engagement outcomes.
}
```

If **ambiguous/multiple** values detected, we will ask user to select one of the values from the list.

#### Client name:
##### Detection
1. Detect client name from the prompt using LLM.
3. Search for client name using the KX Qual API to get client id.
2. If client name is not found in the text, ask user to provide client name.

##### Properties
- `name: string` - Client name from KX Qual API.
- `id: int` - Client id from KX Qual API (Optional).

#### Lead Deloitte member firm/country:
Deloitte member firm or country executing this engagement.

##### Detection
1. Get list of member firms from KX Qual API.
2. Detect member firm/country from the text using LLM and provide list of member firms/countries to the user

##### Properties
- `id: int` - Member firm id from KX Qual API.
- `name: string` - Member firm name from KX Qual API.

#### Engagement dates:
Start and end dates of the engagement, both dates required.
##### Detection
1. Detect engagement dates from the prompt using LLM.

#### Properties
- `start_date: string` - Engagement start date.
- `end_date: string` - Engagement end date.

#### Objective and scope:
What was the key focus areas - enhancing processes, launching new services or something else.

##### Detection
1. Detect objective and scope from the prompt using LLM.
##### Properties
- `objective_and_scope: string` - Engagement objective and scope.

#### Outcomes:
What was the impact? How did this project drives success?

##### Detection
1. Detect outcomes from the prompt using LLM.

##### Properties
- `outcomes: string` - Engagement outcomes.
