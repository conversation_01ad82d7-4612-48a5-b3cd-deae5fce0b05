# Documentation Pages
1. [Qual generation](/docs/qual-generation.md)
2. [Existing Quals app API](/docs/db.md)
3. [DB Schema](/docs/db/README.md)
4. [GenAI API Swagger](/docs/api/openapi.yaml)


# Architecture
![Architecture](images/Architecture-Infrastructure.drawio.png)

---

### **Core Infrastructure Components**
1. **Azure App Service Plan (Linux Plan)**:
   - Hosts the web apps (frontend and backend) and ensures scalable compute resources for the application.

2. **Azure Web Apps**:
   - **Frontend Web App (FE Application)**:
     - Serves the user interface and handles user requests through an application gateway.
   - **Backend Web App (BE API)**:
     - Manages the business logic, APIs, and interacts with other services like Azure Functions, databases, and queues.
   - **Existing Web Applications**:
     - Integrates with pre-existing apps, such as KX FE apps and Quals API, for reusability and connectivity.

3. **Azure Functions**:
   - Processes uploaded files, extracts document information, and triggers workflows.
   - Works with Azure Document Intelligence for extracting content from documents.

---

### **Authentication and Routing**
4. **Azure AD (Active Directory)**:
   - Facilitates user authentication for secure access to the application.

5. **Application Gateway**:
   - Routes requests based on path-based routing (e.g., `/quals`, `/prompt`) to the appropriate web applications.

---

### **Data Storage and Processing**
6. **Azure Storage Account**:
   - **Storage Blobs**:
     - Stores function code/state and processed document files.
   - **Storage Queues**:
     - Acts as triggers for Azure Functions to process queued tasks.

7. **Microsoft SQL Server**:
   - Hosts the application's database (quals db) for storing structured data.

---

### **Integration and Notifications**
8. **SignalR**:
   - Provides real-time notifications to users during processing.

9. **Azure Document Intelligence**:
   - Extracts content and metadata from uploaded documents for further processing.

---

### **Monitoring and Insights**
10. **Application Insights**:
    - Tracks application logs, metrics, and performance for troubleshooting and optimization.

---

### **Workflow**
1. Users interact with the frontend web app, authenticated via Azure AD.
2. Requests are routed through the Application Gateway to the appropriate service.
3. Uploaded files are processed via Azure Functions, leveraging Azure Document Intelligence for content extraction.
4. Data is stored in Azure Storage (Blobs/Queues) and SQL Server, with real-time notifications sent using SignalR.
5. Backend APIs facilitate communication between frontend, Azure Functions, and external systems (e.g., legacy apps).
6. Logs and metrics are monitored using Application Insights.
