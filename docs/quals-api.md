# Existing Quals app API

## Search clients by name
Url: GET /api/client/clients?contains=Del&pageSize=20

Response example:
```json
[
    {
        "id": 76541,
        "name": "4iG Nyilvanosan Mukodo Reszvenytersasag",
        "qualsCount": 2,
        "clientConfidentiality": 1 // 1 - Standard, 2 - Extensive
    }
]
```

## List of member firms
Url: GET /api/project/member-firms

Response example:
```json
[
    {
        "memberFirmId": 0,
        "name": "Afghanistan",
        "id": 390
    },
    {
        "memberFirmId": 0,
        "name": "Aland Islands",
        "id": 392
    }
]
```
