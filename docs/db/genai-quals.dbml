Table QualConversation {
  Id uniqueidentifier [pk]
  QualId uniqueidentifier [not null, ref: > Qual.Id]
  FromDash bit [not null, note: 'True if qual conversation created from Dash task']
  IsCompleted bit [not null, note: 'True if qual conversation is completed, redirected to dash form']
  CreatedAt datetime2(7) [not null]
  CreatedById uniqueidentifier [not null, note: 'User. The user who created the conversation.']
  CreatedByName nvarchar(max) [note: 'The name of the user who created the conversation.']
}

Table QualConversationMessage {
  Id uniqueidentifier [pk]
  QualConversationId uniqueidentifier [not null, ref: > QualConversation.Id]
  Role int [not null, note: 'The role of the message author. 0 = system message, 1 = user message']
  Type int [not null, note: 'The type of the message. 0 = text, 1 = file, 2 = form']
  CreatedAt datetime2(7) [not null]
  Content nvarchar(max) [not null, note: 'The content of the message.']
}

Table QualDocument {
  Id uniqueidentifier [pk, not null]
  QualConversationMessageId uniqueidentifier [not null, ref: > QualConversationMessage.Id]
  FileName nvarchar(max) [not null, note: 'The name of the file.']
  FileSize int [not null, note: 'The size of the file in bytes.']
  FileType nvarchar(max) [not null, note: 'The type of the file.']
}

Table Qual {
  Id uniqueidentifier [pk, not null]
  KxQualId int [note: 'Qual id from KX Qual DB']
  DashTaskId int [note: 'Dash task id if qual created from Dash task']
  QualProjectId uniqueidentifier [ref: > QualProject.Id, note: 'Qual project reference']
}

Table QualProject {
  Id uniqueidentifier [pk, not null]
  ClientName nvarchar(max) [not null, note: 'The name of the client.']
  ClientId int [note: 'Client id from Qual DB']
  LeadMemberFirmId int
  LeadMemberFirmName nvarchar(500) [not null, note: 'The name of the lead member firm.']
  EndDate datetime2(7) [note: 'The end date of engagement']
  StartDate datetime2(7) [note: 'The start date of engagement']
  ObjectivesAndScope nvarchar(max) [note: 'The objectives and scope of the project.']
  Outcomes nvarchar(max) [note: 'The outcomes of the project.']
}
