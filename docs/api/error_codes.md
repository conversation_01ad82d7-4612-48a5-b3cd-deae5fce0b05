# API Error Messages
According to message from <PERSON><PERSON>, the APIs should returns the messages with status codes as following

| **Status** | **Error Type**                | **Description**                                                                             | note       |
|------------|-------------------------------|---------------------------------------------------------------------------------------------|------------|
| 400        | invalid\_request\_error       | The request was malformed, missing required fields, or had invalid parameter values\.       |            |
| 401        | authentication\_error         | The API key was missing, incorrect, or invalid\.                                            |            |
| 403        | permission\_error             | The API key does not have permission to perform the requested action\.                      |            |
| 429        | rate\_limit\_error            | You have hit a rate limit \(requests per minute, tokens per minute, etc\.\)\.               |            |
| 429        | insufficient\_quota           | Your account does not have enough quota to complete the request\.                           |            |
| 503        | service\_unavailable\_error   | The service is temporarily unavailable \(maintenance, overload, etc\.\)\.                   |            |
| 504        | timeout                       | The request took too long to complete\.                                                     |            |
| 404        | not\_found\_error             | The requested resource does not exist\.                                                     |            |
| 409        | conflict\_error               | The request could not be completed due to a conflict \(e\.g\., resource already exists\)\.  |            |
| 500        | server\_error                 | Server encountered an unexpected condition\.                                                |            |
| 502        | bad\_gateway                  | Service is temporarily unavailable, often due to overload\.                                 |            |
| 404        | model\_not\_found             | The specified model does not exist or is not available to your account\.                    |            |
| 429        | context\_length\_exceeded     | The input plus output tokens exceeds the model’s maximum context window\.                   |inapplicable|
| 429        | billing\_hard\_limit\_reached | Your account’s billing limit has been reached\.                                             |inapplicable|

this implemented in app/middleware/exc_to_resp.py::ExceptionToResponseMiddleware
