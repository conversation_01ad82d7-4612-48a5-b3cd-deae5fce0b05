services:
  quals-sqlserver:
    image: mcr.microsoft.com/azure-sql-edge
    platform: linux/amd64
    container_name: quals-sqlserver
    command: /opt/mssql/bin/sqlservr
    ports:
      - "1433:1433"
    environment:
      - ACCEPT_EULA=Y
      - MSSQL_SA_PASSWORD=P@ssw0rd_2024_SQL_Secure!
      - MSSQL_PID=Developer
      - MSSQL_TCP_PORT=1433
    volumes:
      - ~/.docker/volumes/quals-sqlserver:/var/opt/mssql
    restart: always
    cap_add:
      - SYS_PTRACE
    mem_limit: 3g
    healthcheck:
      test: /opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P "P@ssw0rd_2024_SQL_Secure!" -Q "SELECT 1" || exit 1
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  azurite:
    image: mcr.microsoft.com/azure-storage/azurite
    container_name: "azurite"
    hostname: azurite
    restart: always
    ports:
      - "10000:10000"  # Blob service
      - "10001:10001"  # Queue service
      - "10002:10002"  # Table service
    volumes:
      - ~/.docker/volumes/azurite:/data
