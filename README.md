# Introduction

TODO: Give a short introduction of your project. Let this section explain the objectives or the motivation behind
this project.

# Getting Started

TODO: Guide users through getting your code up and running on their own system. In this section you can talk about:

1.  Installation process
1.  Software dependencies
1.  Latest releases
1.  API references

## Prerequisites

- ODBC Driver 17 for SQL Server must be installed on your system

### Installing ODBC Driver on macOS

```bash
# Install Homebrew if not already installed
% /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/master/install.sh)"

# Add Microsoft's tap and update Homebrew
% brew tap microsoft/mssql-release https://github.com/Microsoft/homebrew-mssql-release
% brew update

# Install the driver (accepts EULA automatically)
% HOMEBREW_ACCEPT_EULA=Y brew install msodbcsql17 mssql-tools unixodbc
```

## Installation

1. Install Python **3.12**. The currently used version can be determined from the `pyproject.toml` file.
   - **Alternative**: Use pyenv to manage Python versions:
     ```bash
     % pyenv install 3.12
     % pyenv local 3.12  # Sets 3.12 as the version for this directory
     ```

2. Create a virtual environment for the app. It can be either a virtual environment called `.venv` created at the root
   of the repo. Or it can also be a virtual environment created in any other place, but it should be linked or mounted
   to the `.venv` link or directory at the root of the repo. (NOTE: the name and placement of the virtual environment
   were hardcoded in the PyRight configuration and should be respected to keep PyRight working.) For example:<br><br>
   `% python3 -m venv .venv`<br><br> or <br><br>
   `% python -m venv .venv`<br><br>

3. Activate the virtual environment:<br><br>
   ```bash
   # On macOS/Linux
   % source .venv/bin/activate

   # On Windows
   % .venv\Scripts\activate
   ```

4. Install the app dependencies in the virtual environment. The dependencies can be found in the `app` directory and
   have names like `requirements*.txt`. The main one is `requirements.txt` which keeps the typical dependencies for all
   the environments. Other files were named after the environments they were intended for. They include
   `requirements.txt` and extend it with additional dependencies when required. For example, if we're going to install
   dependencies required for development, we should use the `requirements-dev.txt` file:<br><br>
   `% pip install -r app/requirements-dev.txt`

## Environment Configuration

The application uses environment variables for configuration. You can provide these variables in two ways:

1. Create a `.env` file in the `app` directory (default location)
1. Create the file anywhere and set the `ENV_FILE` environment variable to point to its location

### Environment File Setup

#### Option 1: Default Location

Create a `.env` file in the `app` directory with the variables like in the example `app/.env.sample` as a template
for your configuration.

#### Option 2: Custom Location

1. Create the `.env` file in your preferred location using `app/.env.sample` as a template
1. Set the `ENV_FILE` environment variable to point to your file before calling uvicorn or python command

The application will automatically load these environment variables using python-dotenv.

## Running

### Setting the PYTHONPATH

To ensure the application runs correctly, set the `PYTHONPATH` to the `app` folder. This allows Python to locate the
 application's modules properly.

You can set the `PYTHONPATH` temporarily when running commands:

```bash
% PYTHONPATH=./app uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

Or, export it as an environment variable for the current session:

```bash
% export PYTHONPATH=./app
```

### Creating the Database

First, ensure the SQL Server container is running:

```bash
% docker-compose up -d quals-sqlserver
```

Then create the database by setting the required environment variables and running the creation script:

```bash
% set -a
% source app/.env.sample
% set +a
% ./scripts/create-db.sh
```

### Running Database Migrations

To apply database migrations, launch the following command from the `app` directory:

```bash
% alembic upgrade head
```

This command ensures that the latest database schema changes are applied. Make sure the `PYTHONPATH` is set to the
`app` folder so that Alembic can locate the application modules properly.

### Starting the Application

The app uses the Uvicorn ASGI web server and can run as follows:
```bash
% uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

Or, in case we'd like to redirect all the logging messages from stderr to stdout, including uvicorn ones, but excluding
Python crash logs:
```bash
% uvicorn --log-config=uvicorn_logging.json main:app --host 0.0.0.0 --port 8000 --reload
```

## Configuring git hooks

### Configuring pre-commit

We use `pre-commit` to make it easier to perform some checks before pushing the changes for review and to follow the
coding conventions.

The only thing required to make `pre-commit` work is running

`% pre-commit install --install-hooks`

in the virtual environment after it was created and the development dependencies were installed. This will make a
`pre-commit` hook in `.git/hooks` and install dependencies for all the hooks in `pre-commit`'s own virtual environments.

After this pre-commit will be run every time before a new commit is created. Also, it can be run manually as a regular
command (please run `pre-commit --help` in the app's virtual environment for more information on commands.)

### Configuring post-commit

Copy the `post-commit` file from the `scripts` directory into the `.git/hooks` one and make it executable.

## Azure Storage with Azurite

The application uses Azure Blob Storage for document storage. For local development, we use Azurite, an Azure Storage
emulator.

### Running Azurite with Docker Compose

Azurite is included in the Docker Compose configuration. To start it:

```bash
% docker-compose up -d azurite
```

This will start the Azurite container with the following services:
- Blob service on port 10000
- Queue service on port 10001
- Table service on port 10002

### Testing Blob Storage Connection

You can verify your connection to Azurite using the provided test script:

```bash
% PYTHONPATH=./app python scripts/test_azurite_connection.py
```

This script will:
1. Connect to the local Azurite instance
2. Create a test container
3. Upload a test blob
4. Verify the blob properties
5. Clean up the test resources

If successful, you'll see a confirmation message that Azurite is working correctly.

### Queue Service and Message Format

After content is successfully uploaded to blob storage, a message is sent to the Azure Queue Storage for further
 processing. The application uses a unified queue (`content-analysis-queue`) to handle both documents and text prompts.

The message is a JSON object with the following structure:

```json
{
  "source": {
    "text_prompt": "https://your-storage-account.blob.core.windows.net/uploads-prompts/message-id/prompt.txt",
    "documents": [
      "https://your-storage-account.blob.core.windows.net/uploads/message-id/document1.pdf",
      "https://your-storage-account.blob.core.windows.net/uploads/message-id/document2.docx"
    ]
  },
  "signal_r_connection_id": "your-signalr-connection-id"
}
```

- `source`: Contains the URLs for the content to be processed.
  - `text_prompt`: The URL to a text file containing the user's prompt. (Optional)
  - `documents`: A list of URLs to the documents uploaded by the user. (Optional)
- `signal_r_connection_id`: The SignalR connection ID for sending real-time notifications to the client.

This unified message format allows the Durable Functions orchestrator to process text prompts, documents, or a
combination of both in a single workflow.

### Using Azure Storage Explorer for Local Testing

For a graphical interface to interact with your local Azurite storage:

1. Download and install [Azure Storage Explorer](https://azure.microsoft.com/en-us/features/storage-explorer/)
2. Launch Storage Explorer
3. Connect to Azurite:
   - Click on "Connect to Azure Storage"
   - Select "Attach to a local emulator"
   - Choose "Azurite" and click "Next"
   - Use the default connection name or provide your own
   - Click "Connect"

Once connected, you can browse containers, upload/download blobs, and manage your local storage through the GUI.

The default connection string for local development can be taken from the "Blob Storage" example of
https://learn.microsoft.com/en-us/azure/storage/common/storage-use-azurite?tabs=visual-studio%2Cblob-storage#http-connection-strings

This connection string should also be configured in the `.env.sample` file.


## Testing

Please refer to the instructions from the `app/tests/README.md`.
