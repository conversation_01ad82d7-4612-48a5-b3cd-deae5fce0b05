from datetime import datetime
import logging
import os
from typing import Any

from azure.core.exceptions import ResourceExistsError, ResourceNotFoundError
from azure.storage.blob import BlobServiceClient, ContentSettings


logger = logging.getLogger(__name__)


def _setup_logging() -> None:
    """Configure logging for the script."""
    logging.basicConfig(
        level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s', datefmt='%Y-%m-%d %H:%M:%S'
    )


def _log_blob_properties(properties: Any) -> None:
    """Log blob properties in a structured way."""
    logger.info('Blob information:')
    logger.info('- Blob URL: %s', properties.get('url'))
    logger.info('- Container name: %s', properties.container)
    logger.info('- Blob name: %s', properties.name)
    logger.info('- Content type: %s', properties.content_settings.content_type)
    logger.info('- Size: %d bytes', properties.size)
    logger.info('- ETag: %s', properties.etag)

    logger.debug('All available blob properties:')
    for key, value in vars(properties).items():
        logger.debug('  %s: %s', key, value)


def test_azurite_connection() -> None:
    """Test connection to Azurite blob storage."""
    # Azurite connection string for local development
    env_key_name = 'AZURITE_CONN_STRING'
    connection_string = os.getenv(env_key_name)
    if connection_string is None:
        logger.warning(
            f'Please set the "{env_key_name}" env. variable. Use the full connection string example from https://learn.microsoft.com/en-us/azure/storage/common/storage-use-azurite?tabs=visual-studio%2Cqueue-storage#http-connection-strings'
        )
        return

    # Test container and blob names
    CONTAINER_NAME = 'test-container'
    TEST_BLOB_NAME = f'test-blob-{datetime.now().strftime("%Y%m%d-%H%M%S")}.txt'
    TEST_CONTENT = 'This is a test file to verify Azurite connection.'

    try:
        # 1. Create BlobServiceClient
        logger.info('Testing connection to Azurite...')
        blob_service_client = BlobServiceClient.from_connection_string(connection_string)

        # 2. Create test container
        logger.info("Creating test container '%s'...", CONTAINER_NAME)
        try:
            container_client = blob_service_client.create_container(CONTAINER_NAME)
            logger.info("Container '%s' created successfully!", CONTAINER_NAME)
        except ResourceExistsError:
            container_client = blob_service_client.get_container_client(CONTAINER_NAME)
            logger.info("Container '%s' already exists.", CONTAINER_NAME)

        # 3. Upload test blob
        logger.info("Uploading test blob '%s'...", TEST_BLOB_NAME)
        blob_client = container_client.get_blob_client(TEST_BLOB_NAME)

        # Create content settings
        content_settings = ContentSettings(content_type='text/plain')

        # Upload blob
        blob_client.upload_blob(TEST_CONTENT, overwrite=True, content_settings=content_settings)
        logger.info("Blob '%s' uploaded successfully!", TEST_BLOB_NAME)

        # Get and log blob properties
        properties = blob_client.get_blob_properties()
        _log_blob_properties(properties)

        # 4. Clean up
        logger.info('Cleaning up test resources...')
        blob_client.delete_blob()
        container_client.delete_container()
        logger.info('Test resources cleaned up successfully!')

        logger.info('✅ All tests passed! Azurite is working correctly.')
        assert True

    except ResourceNotFoundError as e:
        logger.error('Resource not found error: %s', e)
        logger.error('Make sure Azurite container is running and ports are correctly mapped.')
        assert False
    except Exception as e:
        logger.error('Error during Azurite connection test: %s', e)
        logger.error('Make sure Azurite container is running and accessible.')
        assert False


if __name__ == '__main__':
    _setup_logging()
    test_azurite_connection()
