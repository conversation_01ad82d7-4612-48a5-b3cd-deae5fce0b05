#!/bin/bash
set -e

MAX_RETRIES=10
SLEEP_TIME=2

echo "Waiting for SQL Server to be ready..."

for i in {1..10}; do
  if sqlcmd -S "$DB_HOST" \
      -U sa \
      -P "$DB_PASSWORD" \
      -Q "IF NOT EXISTS (SELECT * FROM sys.databases WHERE name = N'$DB_NAME') BEGIN CREATE DATABASE [$DB_NAME] END" &> /dev/null; then
    echo "Database $DB_NAME created successfully"
    exit 0
  else
    echo "SQL Server is unavailable - sleeping (attempt $i/$MAX_RETRIES)"
    sleep $SLEEP_TIME
  fi
done

echo "Error: Could not create database after $MAX_RETRIES attempts"
exit 1
