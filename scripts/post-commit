#!/bin/bash

VERSION_FILE='app/version'
THRESHOLD=30  # in seconds

CURRENT_TS=$(date +%s)

if [ -f "${VERSION_FILE}" ]; then
    OLD_TS=$(cat "${VERSION_FILE}")
else
    OLD_TS='0'
fi

# If file is up-to-date, skip
if (( CURRENT_TS - OLD_TS <= THRESHOLD )); then
    exit 0
fi

# Save timestamp in the version file
echo "${CURRENT_TS}" > "${VERSION_FILE}"

# Stage the version file
git add "${VERSION_FILE}"

# Amend the commit to include the version file
git commit --amend --no-edit --no-verify
