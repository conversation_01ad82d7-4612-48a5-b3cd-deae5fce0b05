repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: check-case-conflict
      - id: check-illegal-windows-names
      - id: check-json
      - id: pretty-format-json
        args:
          - '--autofix'
          - '--indent=2'
          - '--no-sort-keys'
      - id: check-toml
      - id: check-yaml
      - id: check-merge-conflict
      - id: mixed-line-ending
        args: ['--fix=lf']
      - id: name-tests-test
        args: ['--pytest-test-first']
        exclude: '^app/tests/fixtures/'
      - id: no-commit-to-branch
        args: ['--branch', 'main', '--branch', 'dev']
      - id: end-of-file-fixer
      - id: trailing-whitespace
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.12.4
    hooks:
      - id: ruff-check
        args: [--fix]
      - id: ruff-format
  - repo: https://github.com/RobertCraigie/pyright-python
    rev: v1.1.403
    hooks:
      - id: pyright
