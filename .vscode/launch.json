{
  "version": "0.2.0",
  "configurations": [
    {
      "type": "debugpy",
      "request": "launch",
      "name": "Launch Uvicorn App",
      "module": "uvicorn",
      "args": [
        "app.main:app",
        "--host",
        "0.0.0.0",
        "--port",
        "8000",
        "--reload"
      ],
      "env": {
        "PYTHONPATH": "${workspaceFolder}/app",
        "ENV_FILE": "${workspaceFolder}/app/.env"
      },
      "python": "${workspaceFolder}/.venv/bin/python",
      "cwd": "${workspaceFolder}",
      "justMyCode": false
    },
    {
      "name": "Attach to Python Functions",
      "type": "debugpy",
      "request": "attach",
      "connect": {
        "host": "localhost",
        "port": 9091
      },
      "preLaunchTask": "func: host start"
    },
    {
      "name": "Python: Pytest",
      "type": "debugpy",
      "request": "launch",
      "module": "pytest",
      "args": [
        "tests",
        "-s",
        "-v"
      ],
      "env": {
        "ENVIRONMENT": "test",
      },
      "justMyCode": true
    }
  ]
}