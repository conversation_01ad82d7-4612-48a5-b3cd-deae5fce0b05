{"python.testing.pytestArgs": ["app/tests"], "python.testing.unittestEnabled": false, "python.testing.pytestEnabled": true, "python.testing.autoTestDiscoverOnSaveEnabled": true, "python.envFile": "${workspaceFolder}/app/.env", "coverage-gutters.xmlname": "coverage.xml", "coverage-gutters.coverageFileNames": ["coverage.xml"], "azureFunctions.deploySubpath": "app/", "azureFunctions.scmDoBuildDuringDeployment": true, "azureFunctions.pythonVenv": "${workspaceFolder}/.venv", "azureFunctions.projectLanguage": "Python", "azureFunctions.projectRuntime": "~4", "debug.internalConsoleOptions": "neverOpen", "azureFunctions.projectLanguageModel": 2, "azureFunctions.projectSubpath": "app/", "snyk.trustedFolders": ["/Users/<USER>/PythonProjects/API-KXQualsAIassistant/"]}