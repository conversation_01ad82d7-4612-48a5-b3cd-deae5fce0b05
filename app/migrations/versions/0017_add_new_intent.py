"""add new intent

Revision ID: 0017
Revises: 0016
Create Date: 2025-06-23 13:02:38.857479

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0017'
down_revision: Union[str, None] = '0016'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        'QualConversationMessage',
        'Intention',
        existing_type=sa.VARCHAR(length=17, collation='SQL_Latin1_General_CP1_CI_AS'),
        type_=sa.Enum(
            'undefined',
            'generate_qual',
            'extraction',
            'example',
            'dash_discard',
            'uncertainty',
            'need_context',
            'user_confirmation',
            'change_engagement_dates',
            name='conversationmessageintention',
        ),
        existing_nullable=True,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        'QualConversationMessage',
        'Intention',
        existing_type=sa.Enum(
            'undefined',
            'generate_qual',
            'extraction',
            'example',
            'dash_discard',
            'uncertainty',
            'need_context',
            'user_confirmation',
            'change_engagement_dates',
            name='conversationmessageintention',
        ),
        type_=sa.VARCHAR(length=17, collation='SQL_Latin1_General_CP1_CI_AS'),
        existing_nullable=True,
    )
    # ### end Alembic commands ###
