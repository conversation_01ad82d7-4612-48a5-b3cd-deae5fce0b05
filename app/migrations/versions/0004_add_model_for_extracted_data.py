"""Add model for extracted data

Revision ID: 0004
Revises: 0003
Create Date: 2025-05-23 01:18:25.035502

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0004'
down_revision: Union[str, None] = '0003'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.execute('DELETE FROM [QualDocument]')
    op.execute('DELETE FROM [QualConversationMessage]')
    op.execute('DELETE FROM [QualConversation]')
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        'QualExtractedData',
        sa.Column('Id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('QualConversationId', sa.Integer(), nullable=False),
        sa.Column('DataSourceType', sa.Enum('kx_dash', 'documents', 'prompt', name='datasourcetype'), nullable=False),
        sa.Column('CreatedAt', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.Column('ActivityName', sa.UnicodeText(), nullable=True),
        sa.Column('ClientName', sa.UnicodeText(), nullable=True),
        sa.Column('LDMFCountry', sa.UnicodeText(), nullable=True),
        sa.Column('Title', sa.UnicodeText(), nullable=True),
        sa.Column('StartDate', sa.Date(), nullable=True),
        sa.Column('EndDate', sa.Date(), nullable=True),
        sa.Column('ClientIndustry', sa.UnicodeText(), nullable=True),
        sa.Column('ClientServices', sa.UnicodeText(), nullable=True),
        sa.Column('TeamAndRoles', sa.UnicodeText(), nullable=True),
        sa.ForeignKeyConstraint(
            ['QualConversationId'],
            ['QualConversation.Id'],
        ),
        sa.PrimaryKeyConstraint('Id'),
        sa.UniqueConstraint('QualConversationId', 'DataSourceType', name='uq_conversation_source_type'),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('QualExtractedData')
    # ### end Alembic commands ###
