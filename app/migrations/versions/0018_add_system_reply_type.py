"""Add system reply type

Revision ID: 0018
Revises: 0017
Create Date: 2025-06-30 16:49:37.321247

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0018'
down_revision: Union[str, None] = '0017'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        'QualConversationMessage',
        sa.Column(
            'SystemReplyType',
            sa.Enum(
                'welcome_message_with_many_dash_tasks',
                'welcome_message',
                'empty',
                'undefined',
                'dash_task_discard',
                'example',
                'brief_description',
                'need_info_initial',
                'need_info_dates',
                'need_info_ldmf_country',
                'need_info_outcomes',
                'need_info_objective_scope',
                'need_info_client_name',
                'outcomes_aggregated_question',
                'confirmed_fields_ready',
                'dates_confirmed',
                'dates_confirmation',
                'dates_ambiguous',
                'additional_data',
                'additional_data_proposal',
                'client_name_confirmed',
                'client_name_single_confirmation',
                'client_name_not_found',
                'client_creation_confirmed',
                'client_name_multiple_options',
                'objective_and_scope_confirmed',
                'confirm_objective_and_scope',
                'ready_to_generate_qual_reply',
                'outcomes_confirmed',
                'ldmf_country_confirmed',
                'ldmf_country_single_confirmation',
                'ldmf_country_multiple_options',
                'ready_to_create_draft_qual',
                'field_saved',
                'client_creation_unsure',
                'client_creation_unsuccessful',
                'client_creation_failed',
                'dates_one_date',
                'missing_required_data',
                'formatted_extracted_data',
                name='systemreplytype',
            ),
            nullable=True,
        ),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('QualConversationMessage', 'SystemReplyType')
    # ### end Alembic commands ###
