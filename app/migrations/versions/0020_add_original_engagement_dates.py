"""Add original engagement dates

Revision ID: 0020
Revises: 0019
Create Date: 2025-07-08 17:55:12.727063

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0020'
down_revision: Union[str, None] = '0019'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('QualExtractedData', sa.Column('StartDateOriginal', sa.UnicodeText(), nullable=True))
    op.add_column('QualExtractedData', sa.Column('EndDateOriginal', sa.UnicodeText(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('QualExtractedData', 'EndDateOriginal')
    op.drop_column('QualExtractedData', 'StartDateOriginal')
    # ### end Alembic commands ###
