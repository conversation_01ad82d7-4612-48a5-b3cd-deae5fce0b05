"""add new intent

Revision ID: 0015
Revises: 0014
Create Date: 2025-06-20 10:51:49.727272

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0015'
down_revision: Union[str, None] = '0014'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        'QualConversation',
        'State',
        existing_type=sa.VARCHAR(length=22, collation='SQL_Latin1_General_CP1_CI_AS'),
        type_=sa.Enum(
            'initial',
            'collecting_client_name',
            'collecting_country',
            'collecting_dates',
            'collecting_objective',
            'collecting_outcomes',
            'collecting_additional_data',
            'data_complete',
            'ready_for_qual_creation',
            name='conversationstate',
        ),
        existing_nullable=False,
        existing_server_default=sa.text("('initial')"),
    )
    op.alter_column(
        'QualConversationMessage',
        'Intention',
        existing_type=sa.VARCHAR(length=13, collation='SQL_Latin1_General_CP1_CI_AS'),
        type_=sa.Enum(
            'undefined',
            'generate_qual',
            'extraction',
            'example',
            'dash_discard',
            'uncertainty',
            'need_context',
            'user_confirmation',
            name='conversationmessageintention',
        ),
        existing_nullable=True,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        'QualConversationMessage',
        'Intention',
        existing_type=sa.Enum(
            'undefined',
            'generate_qual',
            'extraction',
            'example',
            'dash_discard',
            'uncertainty',
            'need_context',
            'user_confirmation',
            name='conversationmessageintention',
        ),
        type_=sa.VARCHAR(length=13, collation='SQL_Latin1_General_CP1_CI_AS'),
        existing_nullable=True,
    )
    op.alter_column(
        'QualConversation',
        'State',
        existing_type=sa.Enum(
            'initial',
            'collecting_client_name',
            'collecting_country',
            'collecting_dates',
            'collecting_objective',
            'collecting_outcomes',
            'collecting_additional_data',
            'data_complete',
            'ready_for_qual_creation',
            name='conversationstate',
        ),
        type_=sa.VARCHAR(length=22, collation='SQL_Latin1_General_CP1_CI_AS'),
        existing_nullable=False,
        existing_server_default=sa.text("('initial')"),
    )
    # ### end Alembic commands ###
