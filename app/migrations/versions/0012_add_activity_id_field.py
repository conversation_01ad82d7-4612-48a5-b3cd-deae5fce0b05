"""Add activity id field

Revision ID: 0012
Revises: 0011
Create Date: 2025-06-09 11:55:37.079572

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0012'
down_revision: Union[str, None] = '0011'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('QualExtractedData', sa.Column('ActivityId', sa.Integer(), nullable=True))
    op.create_index(op.f('ix_QualExtractedData_ActivityId'), 'QualExtractedData', ['ActivityId'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_QualExtractedData_ActivityId'), table_name='QualExtractedData')
    op.drop_column('QualExtractedData', 'ActivityId')
    # ### end Alembic commands ###
