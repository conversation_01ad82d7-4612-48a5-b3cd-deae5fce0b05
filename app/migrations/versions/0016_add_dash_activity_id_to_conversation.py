"""Add dash activity id to conversation

Revision ID: 0016
Revises: 0015
Create Date: 2025-06-20 18:07:36.044786

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mssql


# revision identifiers, used by Alembic.
revision: str = '0016'
down_revision: Union[str, None] = '0015'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('QualConversation', sa.Column('DashActivityId', sa.Integer(), nullable=True))
    op.drop_column('QualConversation', 'FromDash')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('QualConversation', sa.Column('FromDash', mssql.BIT(), autoincrement=False, nullable=False))
    op.drop_column('QualConversation', 'DashActivityId')
    # ### end Alembic commands ###
