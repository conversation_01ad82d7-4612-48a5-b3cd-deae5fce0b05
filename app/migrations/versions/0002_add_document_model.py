"""add document model

Revision ID: 0002
Revises: 0001
Create Date: 2025-04-22 11:10:02.972918

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mssql


# revision identifiers, used by Alembic.
revision: str = '0002'
down_revision: Union[str, None] = '0001'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        'QualDocument',
        sa.Column('Id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('PublicId', mssql.UNIQUEIDENTIFIER(), server_default=sa.text('NEWID()'), nullable=False),
        sa.Column('QualConversationMessageId', sa.Integer(), nullable=False),
        sa.Column('FileName', sa.String(), nullable=False),
        sa.Column('FileSize', sa.Integer(), nullable=False),
        sa.Column('FileType', sa.String(), nullable=False),
        sa.Column('FileUrl', sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ['QualConversationMessageId'],
            ['QualConversationMessage.Id'],
        ),
        sa.PrimaryKeyConstraint('Id'),
        sa.UniqueConstraint('PublicId'),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('QualDocument')
    # ### end Alembic commands ###
