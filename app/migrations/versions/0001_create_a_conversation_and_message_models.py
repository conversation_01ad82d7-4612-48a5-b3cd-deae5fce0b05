"""create a conversation and message models

Revision ID: 0001
Revises:
Create Date: 2025-04-14 16:28:16.595025

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mssql


# revision identifiers, used by Alembic.
revision: str = '0001'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        'QualConversation',
        sa.Column('Id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('PublicId', mssql.UNIQUEIDENTIFIER(), server_default=sa.text('NEWID()'), nullable=False),
        sa.Column('QualId', sa.String(), nullable=False),
        sa.Column('FromDash', sa.<PERSON>(), nullable=False),
        sa.Column('IsCompleted', sa.<PERSON>an(), nullable=False),
        sa.Column('CreatedAt', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.Column('CreatedById', mssql.UNIQUEIDENTIFIER(), nullable=False),
        sa.Column('CreatedByName', sa.String(), nullable=True),
        sa.PrimaryKeyConstraint('Id'),
        sa.UniqueConstraint('PublicId'),
    )
    op.create_table(
        'QualConversationMessage',
        sa.Column('Id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('PublicId', mssql.UNIQUEIDENTIFIER(), server_default=sa.text('NEWID()'), nullable=False),
        sa.Column('QualConversationId', sa.Integer(), nullable=False),
        sa.Column('Role', sa.Enum('SYSTEM', 'USER', name='messagerole'), nullable=False),
        sa.Column('Type', sa.Enum('TEXT', 'FILE', 'FORM', name='messagetype'), nullable=False),
        sa.Column('CreatedAt', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.Column('Content', sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ['QualConversationId'],
            ['QualConversation.Id'],
        ),
        sa.PrimaryKeyConstraint('Id'),
        sa.UniqueConstraint('PublicId'),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('QualConversationMessage')
    op.drop_table('QualConversation')
    # ### end Alembic commands ###
