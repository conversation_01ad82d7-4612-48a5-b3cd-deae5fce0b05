"""new reply types

Revision ID: 0019
Revises: 0018
Create Date: 2025-07-03 13:37:14.941135

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0019'
down_revision: Union[str, None] = '0018'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        'QualConversationMessage',
        'SystemReplyType',
        existing_type=sa.VARCHAR(length=36, collation='SQL_Latin1_General_CP1_CI_AS'),
        type_=sa.Enum(
            'welcome_message_with_many_dash_tasks',
            'welcome_message_with_one_dash_task',
            'welcome_message',
            'empty',
            'undefined',
            'example',
            'brief_description',
            'need_info_initial',
            'need_info_dates',
            'need_info_ldmf_country',
            'need_info_outcomes',
            'need_info_objective_scope',
            'need_info_client_name',
            'outcomes_aggregated_question',
            'confirmed_fields_ready',
            'dates_confirmed',
            'dates_confirmation',
            'dates_ambiguous',
            'additional_data',
            'additional_data_proposal',
            'client_name_confirmed',
            'client_name_single_confirmation',
            'client_name_not_found',
            'client_creation_confirmed',
            'client_name_multiple_options',
            'objective_and_scope_confirmed',
            'confirm_objective_and_scope',
            'ready_to_generate_qual_reply',
            'outcomes_confirmed',
            'ldmf_country_confirmed',
            'ldmf_country_single_confirmation',
            'ldmf_country_multiple_options',
            'ready_to_create_draft_qual',
            'field_saved',
            'client_creation_unsure',
            'client_creation_unsuccessful',
            'client_creation_failed',
            'dates_one_date',
            'extracted_data_ldmf_missing',
            'extracted_data_date_interval_missing',
            'extracted_data_objective_scope_missing',
            'extracted_data_outcomes_missing',
            'missing_required_data',
            'formatted_extracted_data',
            name='systemreplytype',
        ),
        existing_nullable=True,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        'QualConversationMessage',
        'SystemReplyType',
        existing_type=sa.Enum(
            'welcome_message_with_many_dash_tasks',
            'welcome_message_with_one_dash_task',
            'welcome_message',
            'empty',
            'undefined',
            'example',
            'brief_description',
            'need_info_initial',
            'need_info_dates',
            'need_info_ldmf_country',
            'need_info_outcomes',
            'need_info_objective_scope',
            'need_info_client_name',
            'outcomes_aggregated_question',
            'confirmed_fields_ready',
            'dates_confirmed',
            'dates_confirmation',
            'dates_ambiguous',
            'additional_data',
            'additional_data_proposal',
            'client_name_confirmed',
            'client_name_single_confirmation',
            'client_name_not_found',
            'client_creation_confirmed',
            'client_name_multiple_options',
            'objective_and_scope_confirmed',
            'confirm_objective_and_scope',
            'ready_to_generate_qual_reply',
            'outcomes_confirmed',
            'ldmf_country_confirmed',
            'ldmf_country_single_confirmation',
            'ldmf_country_multiple_options',
            'ready_to_create_draft_qual',
            'field_saved',
            'client_creation_unsure',
            'client_creation_unsuccessful',
            'client_creation_failed',
            'dates_one_date',
            'extracted_data_ldmf_missing',
            'extracted_data_date_interval_missing',
            'extracted_data_objective_scope_missing',
            'extracted_data_outcomes_missing',
            'missing_required_data',
            'formatted_extracted_data',
            name='systemreplytype',
        ),
        type_=sa.VARCHAR(length=36, collation='SQL_Latin1_General_CP1_CI_AS'),
        existing_nullable=True,
    )
    # ### end Alembic commands ###
