"""message change

Revision ID: 0003
Revises: 0002
Create Date: 2025-05-13 12:34:38.491753

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0003'
down_revision: Union[str, None] = '0002'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        'QualConversationMessage',
        'Type',
        existing_type=sa.VARCHAR(length=4, collation='SQL_Latin1_General_CP1_CI_AS'),
        type_=sa.Enum('TEXT', 'FILE', 'FORM', 'TEXT_WITH_FILE', name='messagetypedb'),
        existing_nullable=False,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        'QualConversationMessage',
        'Type',
        existing_type=sa.Enum('TEXT', 'FILE', 'FORM', 'TEXT_WITH_FILE', name='messagetypedb'),
        type_=sa.VARCHAR(length=4, collation='SQL_Latin1_General_CP1_CI_AS'),
        existing_nullable=False,
    )
    # ### end Alembic commands ###
