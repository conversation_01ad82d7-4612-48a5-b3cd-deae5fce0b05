"""Add columns to QualExtractedData

Revision ID: 0010
Revises: 0009
Create Date: 2025-06-03 15:12:03.264430

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0010'
down_revision: Union[str, None] = '0009'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('createdat_index', table_name='QualConversationMessage')
    op.create_index(
        'IX__QualConversationMessage__CreatedAt_Id', 'QualConversationMessage', ['CreatedAt', 'Id'], unique=False
    )
    op.add_column('QualExtractedData', sa.Column('ObjectiveAndScope', sa.UnicodeText(), nullable=True))
    op.add_column('QualExtractedData', sa.Column('Outcomes', sa.UnicodeText(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('QualExtractedData', 'Outcomes')
    op.drop_column('QualExtractedData', 'ObjectiveAndScope')
    op.drop_index('IX__QualConversationMessage__CreatedAt_Id', table_name='QualConversationMessage')
    op.create_index('createdat_index', 'QualConversationMessage', ['CreatedAt', 'Id'], unique=False)
    # ### end Alembic commands ###
