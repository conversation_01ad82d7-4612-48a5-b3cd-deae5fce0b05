"""add suggested prompts

Revision ID: 0014
Revises: 0013
Create Date: 2025-06-09 17:14:47.566817

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0014'
down_revision: Union[str, None] = '0013'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        'QualConversationMessage', sa.Column('SuggestedPrompts', sa.UnicodeText(), nullable=False, server_default='[]')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('QualConversationMessage', 'SuggestedPrompts')
    # ### end Alembic commands ###
