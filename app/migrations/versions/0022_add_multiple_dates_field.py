"""Add multiple dates field

Revision ID: 0022
Revises: 0021
Create Date: 2025-07-22 12:18:38.260972

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0022'
down_revision: Union[str, None] = '0021'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('QualExtractedData', sa.Column('MultipleDates', sa.Bo<PERSON>an(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('QualExtractedData', 'MultipleDates')
    # ### end Alembic commands ###
