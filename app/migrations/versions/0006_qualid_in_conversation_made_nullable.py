"""QualId in Conversation made nullable

Revision ID: 0006
Revises: 0005
Create Date: 2025-05-28 23:05:25.934624

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0006'
down_revision: Union[str, None] = '0005'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        'QualConversation', 'QualId', existing_type=sa.VARCHAR(collation='SQL_Latin1_General_CP1_CI_AS'), nullable=True
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        'QualConversation', 'QualId', existing_type=sa.VARCHAR(collation='SQL_Latin1_General_CP1_CI_AS'), nullable=False
    )
    # ### end Alembic commands ###
