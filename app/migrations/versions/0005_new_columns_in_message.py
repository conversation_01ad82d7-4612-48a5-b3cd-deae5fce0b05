"""New columns in message

Revision ID: 0005
Revises: 0004
Create Date: 2025-05-26 02:30:11.088801

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0005'
down_revision: Union[str, None] = '0004'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.execute('DELETE FROM [QualDocument]')
    op.execute('DELETE FROM [QualConversationMessage]')
    op.execute('DELETE FROM [QualConversation]')
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('QualConversationMessage', sa.Column('Options', sa.UnicodeText(), nullable=False))
    op.add_column('QualConversationMessage', sa.Column('SelectedOption', sa.UnicodeText(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('QualConversationMessage', 'SelectedOption')
    op.drop_column('QualConversationMessage', 'Options')
    # ### end Alembic commands ###
