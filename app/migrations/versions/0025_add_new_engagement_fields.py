"""add new engagement fields

Revision ID: 0025
Revises: 0024
Create Date: 2025-07-16 18:46:56.422083

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0025'
down_revision: Union[str, None] = '0024'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('QualExtractedData', sa.Column('BusinessIssues', sa.UnicodeText(), nullable=True))
    op.add_column('QualExtractedData', sa.Column('ScopeApproach', sa.UnicodeText(), nullable=True))
    op.add_column('QualExtractedData', sa.Column('ValueDelivered', sa.UnicodeText(), nullable=True))
    op.add_column('QualExtractedData', sa.Column('EngagementSummary', sa.UnicodeText(), nullable=True))
    op.add_column('QualExtractedData', sa.Column('OneLineDescription', sa.UnicodeText(), nullable=True))
    op.add_column('QualExtractedData', sa.Column('ClientReferences', sa.UnicodeText(), nullable=True))
    op.add_column('QualExtractedData', sa.Column('ClientNameSharing', sa.UnicodeText(), nullable=True))
    op.add_column('QualExtractedData', sa.Column('ClientIndustry', sa.UnicodeText(), nullable=True))
    op.add_column('QualExtractedData', sa.Column('EngagementDates', sa.UnicodeText(), nullable=True))
    op.add_column('QualExtractedData', sa.Column('EngagementLocations', sa.UnicodeText(), nullable=True))
    op.add_column('QualExtractedData', sa.Column('EngagementFeeDisplay', sa.UnicodeText(), nullable=True))
    op.add_column('QualExtractedData', sa.Column('ClientServices', sa.UnicodeText(), nullable=True))
    op.add_column('QualExtractedData', sa.Column('SourceOfWork', sa.UnicodeText(), nullable=True))
    op.add_column('QualExtractedData', sa.Column('QualUsage', sa.UnicodeText(), nullable=True))
    op.add_column('QualExtractedData', sa.Column('TeamRoles', sa.UnicodeText(), nullable=True))
    op.add_column('QualExtractedData', sa.Column('Approver', sa.UnicodeText(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('QualExtractedData', 'Approver')
    op.drop_column('QualExtractedData', 'TeamRoles')
    op.drop_column('QualExtractedData', 'QualUsage')
    op.drop_column('QualExtractedData', 'SourceOfWork')
    op.drop_column('QualExtractedData', 'ClientServices')
    op.drop_column('QualExtractedData', 'EngagementFeeDisplay')
    op.drop_column('QualExtractedData', 'EngagementLocations')
    op.drop_column('QualExtractedData', 'EngagementDates')
    op.drop_column('QualExtractedData', 'ClientIndustry')
    op.drop_column('QualExtractedData', 'ClientNameSharing')
    op.drop_column('QualExtractedData', 'ClientReferences')
    op.drop_column('QualExtractedData', 'OneLineDescription')
    op.drop_column('QualExtractedData', 'EngagementSummary')
    op.drop_column('QualExtractedData', 'ValueDelivered')
    op.drop_column('QualExtractedData', 'ScopeApproach')
    op.drop_column('QualExtractedData', 'BusinessIssues')
    # ### end Alembic commands ###
