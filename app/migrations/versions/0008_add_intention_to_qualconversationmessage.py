"""Add intention to QualConversationMessage

Revision ID: 0008
Revises: 0007
Create Date: 2025-05-29 14:26:45.019055

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0008'
down_revision: Union[str, None] = '0007'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        'QualConversationMessage',
        sa.Column(
            'Intention',
            sa.Enum(
                'undefined',
                'generate_qual',
                'extraction',
                'example',
                'dash_discard',
                'uncertainty',
                'need_context',
                name='conversationmessageintention',
            ),
            nullable=True,
        ),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('QualConversationMessage', 'Intention')
    # ### end Alembic commands ###
