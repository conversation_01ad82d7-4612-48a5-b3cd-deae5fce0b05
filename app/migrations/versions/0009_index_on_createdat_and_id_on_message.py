"""index on createdat and id on message

Revision ID: 0009
Revises: 0008
Create Date: 2025-05-31 15:02:44.554093

"""

from typing import Sequence, Union

from alembic import op


# revision identifiers, used by Alembic.
revision: str = '0009'
down_revision: Union[str, None] = '0008'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('createdat_index', 'QualConversationMessage', ['CreatedAt', 'Id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('createdat_index', table_name='QualConversationMessage')
    # ### end Alembic commands ###
