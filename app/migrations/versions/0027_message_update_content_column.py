"""message_update_content_column

Revision ID: 0027
Revises: 0026
Create Date: 2025-07-30 12:05:08.580766

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0027'
down_revision: Union[str, None] = '0026'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('QualConversationMessage', sa.Column('Translation', sa.UnicodeText(), nullable=True))
    op.alter_column(
        'QualConversationMessage',
        'Content',
        existing_type=sa.String(length=4, collation='SQL_Latin1_General_CP1_CI_AS'),
        type_=sa.UnicodeText(),
        existing_nullable=False,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('QualConversationMessage', 'Translation')
    op.alter_column(
        'QualConversationMessage',
        'Content',
        existing_type=sa.UnicodeText(),
        type_=sa.String(length=4, collation='SQL_Latin1_General_CP1_CI_AS'),
        existing_nullable=False,
    )
    # ### end Alembic commands ###
