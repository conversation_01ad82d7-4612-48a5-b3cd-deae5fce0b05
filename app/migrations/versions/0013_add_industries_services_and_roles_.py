"""Add Industries, Services and Roles extraction

Revision ID: 0013
Revises: 0012
Create Date: 2025-06-09 14:18:54.277517

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0013'
down_revision: Union[str, None] = '0012'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('QualExtractedData', sa.Column('Industries', sa.UnicodeText(), nullable=True))
    op.add_column('QualExtractedData', sa.Column('Services', sa.UnicodeText(), nullable=True))
    op.add_column('QualExtractedData', sa.Column('Roles', sa.UnicodeText(), nullable=True))
    op.drop_column('QualExtractedData', 'TeamAndRoles')
    op.drop_column('QualExtractedData', 'ClientIndustry')
    op.drop_column('QualExtractedData', 'ClientServices')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        'QualExtractedData',
        sa.Column(
            'ClientServices', sa.NVARCHAR(collation='SQL_Latin1_General_CP1_CI_AS'), autoincrement=False, nullable=True
        ),
    )
    op.add_column(
        'QualExtractedData',
        sa.Column(
            'ClientIndustry', sa.NVARCHAR(collation='SQL_Latin1_General_CP1_CI_AS'), autoincrement=False, nullable=True
        ),
    )
    op.add_column(
        'QualExtractedData',
        sa.Column(
            'TeamAndRoles', sa.NVARCHAR(collation='SQL_Latin1_General_CP1_CI_AS'), autoincrement=False, nullable=True
        ),
    )
    op.drop_column('QualExtractedData', 'Roles')
    op.drop_column('QualExtractedData', 'Services')
    op.drop_column('QualExtractedData', 'Industries')
    # ### end Alembic commands ###
