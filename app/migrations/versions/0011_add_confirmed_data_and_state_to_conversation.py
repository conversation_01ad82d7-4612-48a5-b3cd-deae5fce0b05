"""Add confirmed_data and state to conversation

Revision ID: 0011
Revises: 0010
Create Date: 2025-01-27 10:00:00.000000

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0011'
down_revision: Union[str, None] = '0010'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('QualConversation', sa.Column('ConfirmedData', sa.UnicodeText(), nullable=True))
    op.add_column(
        'QualConversation',
        sa.Column(
            'State',
            sa.Enum(
                'initial',
                'collecting_client_name',
                'collecting_country',
                'collecting_dates',
                'collecting_objective',
                'collecting_outcomes',
                'data_complete',
                name='conversationstate',
            ),
            nullable=False,
            server_default='initial',
        ),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('QualConversation', 'State')
    op.drop_column('QualConversation', 'ConfirmedData')
    # ### end Alembic commands ###
