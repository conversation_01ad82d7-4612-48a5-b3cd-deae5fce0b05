"""add processing table

Revision ID: 0007
Revises: 0006
Create Date: 2025-05-30 10:59:02.240854

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

from models.qual_processing_message import JSONEncodedDict


# revision identifiers, used by Alembic.
revision: str = '0007'
down_revision: Union[str, None] = '0006'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        'QualProcessingMessage',
        sa.Column('Id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('Status', sa.String(), nullable=False),
        sa.Column('Message', sa.String(), nullable=True),
        sa.Column('Metadata', JSONEncodedDict(), nullable=True),
        sa.Column('QualConversationMessageId', sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(
            ['QualConversationMessageId'],
            ['QualConversationMessage.Id'],
        ),
        sa.PrimaryKeyConstraint('Id'),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('QualProcessingMessage')
    # ### end Alembic commands ###
