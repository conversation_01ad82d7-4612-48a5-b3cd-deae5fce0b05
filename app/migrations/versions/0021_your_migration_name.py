"""extend intention values

Revision ID: 0021
Revises: 0020
Create Date: 2025-07-19 18:55:23.747323

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0021'
down_revision: Union[str, None] = '0020'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        'QualConversationMessage',
        'Intention',
        existing_type=sa.VARCHAR(length=23, collation='SQL_Latin1_General_CP1_CI_AS'),
        type_=sa.Enum(
            'undefined',
            'generate_qual',
            'extraction',
            'example',
            'dash_discard',
            'uncertainty',
            'need_context',
            'user_confirmation',
            'user_denial',
            'change_engagement_dates',
            'provide_client_name',
            'manual_ldmf_input',
            'navigate_to_resource_page',
            name='conversationmessageintention',
        ),
        existing_nullable=True,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        'QualConversationMessage',
        'Intention',
        existing_type=sa.Enum(
            'undefined',
            'generate_qual',
            'extraction',
            'example',
            'dash_discard',
            'uncertainty',
            'need_context',
            'user_confirmation',
            'user_denial',
            'change_engagement_dates',
            'provide_client_name',
            'manual_ldmf_input',
            'navigate_to_resource_page',
            name='conversationmessageintention',
        ),
        type_=sa.VARCHAR(length=23, collation='SQL_Latin1_General_CP1_CI_AS'),
        existing_nullable=True,
    )
    # ### end Alembic commands ###
