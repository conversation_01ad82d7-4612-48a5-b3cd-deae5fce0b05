from logging.config import fileConfig
import os
from pathlib import Path

from alembic import context
from sqlalchemy import engine_from_config, pool

from config import settings
from core.db import Base
from models import *  # noqa


# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Set the revision_environment to True to access the revision_context
config.set_main_option('revision_environment', 'true')


def get_next_revision_id():
    """
    Generate the next sequential integer revision ID.

    Returns:
        str: The next revision ID as a string.
    """
    versions_dir = Path(__file__).parent / 'versions'

    # If the directory doesn't exist or is empty, start with 1
    if not os.path.exists(versions_dir) or not os.listdir(versions_dir):
        return '0001'

    revision_files = (f for f in os.listdir(versions_dir) if f.endswith('.py'))

    revision_ids = []
    for filename in revision_files:
        try:
            # Try to extract the revision ID from the filename
            # Assuming format is like "0001_initial.py"
            revision_id = filename.split('_')[0]
            if revision_id.isdigit():
                revision_ids.append(int(revision_id))
        except (IndexError, ValueError):
            continue

    if not revision_ids:
        return '0001'

    return f'{max(revision_ids) + 1:04d}'


# Override the default revision ID generator with our custom one
def process_revision_directives(context, revision, directives):
    # Extract the revision instructions from the directives
    if directives[0].upgrade_ops.is_empty():
        directives.clear()
        return

    # Set the revision ID to our custom sequential integer
    directives[0].rev_id = get_next_revision_id()


# Interpret the config file for Python logging.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

target_metadata = Base.metadata


def get_url():
    # For Alembic, we'll use the synchronous driver
    return settings.db.uri.replace('mssql+aioodbc', 'mssql+pyodbc')


def run_migrations_offline():
    url = get_url()
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={'paramstyle': 'named'},
        process_revision_directives=process_revision_directives,
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online():
    configuration = config.get_section(config.config_ini_section)
    if configuration is None:
        configuration = {}
    configuration['sqlalchemy.url'] = get_url()

    connectable = engine_from_config(
        configuration,
        prefix='sqlalchemy.',
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection,
            target_metadata=target_metadata,
            process_revision_directives=process_revision_directives,
        )

        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
