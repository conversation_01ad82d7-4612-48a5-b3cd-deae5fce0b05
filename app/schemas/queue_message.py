from uuid import UUID

from pydantic import HttpUrl

from constants.extracted_data import Tense
from core.schemas import CustomModel


__all__ = ['QualQueueMessage', 'Source']


class Source(CustomModel):
    text_prompt: HttpUrl | None = None
    documents: list[HttpUrl] | None = None
    message_ids: list[UUID] | None = None


class QualQueueMessage(CustomModel):
    source: Source
    signal_r_connection_id: str
    tense: Tense | None = None
