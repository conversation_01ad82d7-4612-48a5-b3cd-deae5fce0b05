from uuid import UUID

from fastapi import UploadFile
from pydantic import ConfigDict, Field, HttpUrl

from core.schemas import CustomModel


__all__ = ['DocumentCreationRequest', 'DocumentResponse']


class DocumentCreationRequest(CustomModel):
    """Schema for creating a new document."""

    conversation_id: UUID
    files: list[UploadFile]
    message_id: UUID


class DocumentResponse(CustomModel):
    """Schema for document response."""

    id: UUID = Field(validation_alias='PublicId')
    message_id: UUID = Field(validation_alias='MessagePublicId')
    file_name: str = Field(validation_alias='FileName')
    file_size: int = Field(validation_alias='FileSize')
    file_type: str = Field(validation_alias='FileType')
    file_url: HttpUrl = Field(validation_alias='FileUrl')

    model_config = ConfigDict(
        from_attributes=True,
    )
