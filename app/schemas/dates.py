from datetime import date

from pydantic import BaseModel, field_validator


class DatesLLMResponse(BaseModel):
    date_1: date | None = None
    date_2: date | None = None
    more_than_two_dates: bool | None = None

    @field_validator('date_1', 'date_2', mode='before')
    @classmethod
    def parse_date_str(cls, value: str | None) -> date | None:
        if not value or value == 'null':
            return None
        try:
            return date.fromisoformat(value)
        except ValueError:
            raise ValueError(f'{value} date must be a valid date in ISO format')
