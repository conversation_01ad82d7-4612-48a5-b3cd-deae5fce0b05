from dataclasses import dataclass

from constants.extracted_data import FieldCompletionStatus, ProgressStatus


@dataclass
class FieldStatusInfo:
    """Information about the status of a specific field."""

    status: FieldCompletionStatus
    value: str | list[str] | None
    display_name: str


@dataclass
class ProgressInfo:
    """Information about the overall progress of field collection."""

    total: int
    completed: int
    pending_confirmation: int
    missing: int
    percentage: int
    status: ProgressStatus
