from datetime import datetime
import json

from constants.extracted_data import ConversationState, Tense
from core.schemas import CustomModel


__all__ = ['ConfirmedData']


class ConfirmedData(CustomModel):
    """Schema for user-confirmed qual data."""

    # Core fields (existing)
    client_name: str | None = None
    ldmf_country: str | None = None
    date_intervals: tuple[str | None, str | None] | None = None
    objective_and_scope: str | None = None
    outcomes: str | None = None
    proposed_client_name: str | None = None  # For tracking client names during creation flow
    last_confirmed_field: str | None = None  # Track what field was most recently confirmed

    # Engagement Description fields
    business_issues: str | None = None
    scope_approach: str | None = None
    value_delivered: str | None = None
    engagement_summary: str | None = None
    one_line_description: str | None = None

    # Engagement Details fields
    client_references: str | None = None
    client_name_sharing: str | None = None
    client_industry: str | None = None
    engagement_dates: str | None = None
    engagement_locations: str | None = None
    engagement_fee_display: str | None = None
    client_services: str | None = None
    source_of_work: str | None = None

    # Usage & Team fields
    qual_usage: str | None = None
    team_roles: str | None = None
    approver: str | None = None

    @classmethod
    def from_json_string(cls, json_str: str | None) -> 'ConfirmedData':
        """Create ConfirmedData from JSON string stored in database."""
        if not json_str:
            return cls()
        try:
            data = json.loads(json_str)
            return cls.model_validate(data)
        except (json.JSONDecodeError, ValueError):
            return cls()

    def to_json_string(self) -> str:
        """Convert ConfirmedData to JSON string for database storage."""
        return json.dumps(self.model_dump(exclude_none=True), default=str)

    @property
    def proposed_client_name_is_valid(self) -> bool:
        return self.proposed_client_name is not None and len(self.proposed_client_name.strip()) > 0

    @property
    def client_name_is_valid(self) -> bool:
        return self.client_name is not None and len(self.client_name.strip()) > 0

    @property
    def required_fields_are_complete(self) -> bool:
        """Check if every field is not None and has values (non-empty for lists/strings)."""
        return self.required_fields_except_client_name_are_complete and self.client_name_is_valid

    @property
    def required_fields_except_client_name_are_complete(self) -> bool:
        start_date, end_date = self.date_intervals or (None, None)
        return all(
            (
                self.ldmf_country is not None and len(self.ldmf_country.strip()) > 0,
                start_date is not None,
                end_date is not None,
                self.objective_and_scope is not None and self.objective_and_scope.strip() != '',
                self.outcomes is not None and self.outcomes.strip() != '',
            )
        )

    @property
    def is_empty(self) -> bool:
        return all(
            [
                self.client_name is None,
                self.ldmf_country is None,
                self.date_intervals is None,
                self.objective_and_scope is None,
                self.outcomes is None,
            ]
        )

    def get_current_conversation_state(self) -> ConversationState:
        """
        Determines the current conversation state based on the completeness of confirmed data fields.
        """
        if not self.client_name:
            return ConversationState.COLLECTING_CLIENT_NAME
        if not self.ldmf_country:
            return ConversationState.COLLECTING_COUNTRY
        if not self.date_intervals:
            return ConversationState.COLLECTING_DATES
        if not self.objective_and_scope:
            return ConversationState.COLLECTING_OBJECTIVE
        if not self.outcomes:
            return ConversationState.COLLECTING_OUTCOMES
        return ConversationState.DATA_COMPLETE

    @property
    def all_fields_none(self) -> bool:
        """Return True if all fields are None or empty, False otherwise."""
        return (
            not self.client_name
            and not self.ldmf_country
            and not self.date_intervals
            and self.objective_and_scope is None
            and self.outcomes is None
        )

    @property
    def tense(self) -> Tense:
        """Checks if end date is in the future."""
        if not self.date_intervals:
            return Tense.PAST
        _, end_date_str = self.date_intervals
        if not end_date_str:
            return Tense.PAST
        try:
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
        except ValueError:
            # Handle cases where the date string might be in an unexpected format
            return Tense.PAST
        return Tense.PRESENT if end_date > datetime.now().date() else Tense.PAST
