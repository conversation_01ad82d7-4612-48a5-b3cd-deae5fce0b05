from typing import Any
from uuid import UUID

from pydantic import Field

from core.schemas import CustomModel


__all__ = ['ProcessingStatusUpdatePayload']


class ProcessingStatusUpdatePayload(CustomModel):
    """
    Pydantic model to define the expected payload for updating processing status.
    """

    message_id: UUID
    status: str = Field(..., description="The new processing status (e.g., 'pending', 'completed', 'failed').")
    message: str | None = Field(None, description='An optional descriptive message about the status.')
    metadata: dict[str, Any] | None = Field(None, description='Optional extra key-value metadata.')
