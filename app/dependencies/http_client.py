from typing import Annotated, AsyncGenerator

from fastapi import Depends

from config import settings
from core.http_client import CustomAsyncClient


__all__ = ['HTTPClientDep']


async def get_http_client() -> AsyncGenerator[CustomAsyncClient, None]:
    """
    Dependency that provides a new HTTP client for each request.
    Each request gets its own client that is properly cleaned up after use.
    """
    async with CustomAsyncClient(settings=settings) as client:
        yield client


HTTPClientDep = Annotated[CustomAsyncClient, Depends(get_http_client)]
