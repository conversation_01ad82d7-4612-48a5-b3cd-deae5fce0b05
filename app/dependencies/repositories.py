from typing import Annotated

from fastapi import Depends

from config import settings
from repositories import (
    ConversationMessageRepository,
    ConversationRepository,
    CountriesBlobRepository,
    DocumentBlobRepository,
    DocumentDbRepository,
    DocumentQueueRepository,
    IndustryRepository,
    KXDashRepository,
    LDMFCountriesRepository,
    OpenAIRepository,
    ProcessingMessageRepository,
    QualsClientsRepository,
    RoleRepository,
    ServiceRepository,
)
from repositories.extracted_data import ExtractedDataRepository

from .db import DbSessionDep
from .http_client import HTTPClientDep


__all__ = [
    'ConversationRepositoryDep',
    'ConversationMessageRepositoryDep',
    'DocumentDbRepositoryDep',
    'DocumentBlobRepositoryDep',
    'CountriesBlobRepositoryDep',
    'QualsClientsRepositoryDep',
    'KXDashRepositoryDep',
    'ExtractedDataRepositoryDep',
    'DocumentQueueRepositoryDep',
    'IndustryRepositoryDep',
    'RoleRepositoryDep',
    'ServiceRepositoryDep',
    'LDMFCountriesRepositoryDep',
]


def get_openai_repository() -> OpenAIRepository:
    """Get the OpenAI service for dependency injection."""
    return OpenAIRepository()


OpenAIRepositoryDep = Annotated[OpenAIRepository, Depends(get_openai_repository)]


def get_conversation_repository(db_session: DbSessionDep) -> ConversationRepository:
    return ConversationRepository(db_session)


ConversationRepositoryDep = Annotated[ConversationRepository, Depends(get_conversation_repository)]


def get_processing_message_repository(db_session: DbSessionDep) -> ProcessingMessageRepository:
    return ProcessingMessageRepository(db_session=db_session)


ProcessingMessageRepositoryDep = Annotated[ProcessingMessageRepository, Depends(get_processing_message_repository)]


def get_conversation_message_repository(
    db_session: DbSessionDep, conversation_repository: ConversationRepositoryDep
) -> ConversationMessageRepository:
    return ConversationMessageRepository(db_session=db_session, conversation_repository=conversation_repository)


ConversationMessageRepositoryDep = Annotated[
    ConversationMessageRepository, Depends(get_conversation_message_repository)
]


def get_document_db_repository(db_session: DbSessionDep) -> DocumentDbRepository:
    return DocumentDbRepository(db_session)


DocumentDbRepositoryDep = Annotated[DocumentDbRepository, Depends(get_document_db_repository)]


async def get_document_blob_repository() -> DocumentBlobRepository:
    repo = DocumentBlobRepository(settings.document_storage.connection_string)
    await repo.initialize()
    return repo


DocumentBlobRepositoryDep = Annotated[DocumentBlobRepository, Depends(get_document_blob_repository)]


async def get_countries_blob_repository() -> CountriesBlobRepository:
    repo = CountriesBlobRepository(
        settings.countries_storage.connection_string, settings.countries_storage.container_name
    )
    await repo.initialize()
    return repo


CountriesBlobRepositoryDep = Annotated[CountriesBlobRepository, Depends(get_countries_blob_repository)]


def get_kx_dash_repository(http_client: HTTPClientDep) -> KXDashRepository:
    return KXDashRepository(http_client)


KXDashRepositoryDep = Annotated[KXDashRepository, Depends(get_kx_dash_repository)]


def get_quals_clients_repository(http_client: HTTPClientDep) -> QualsClientsRepository:
    return QualsClientsRepository(http_client)


QualsClientsRepositoryDep = Annotated[QualsClientsRepository, Depends(get_quals_clients_repository)]


def get_ldmf_countries_repository(http_client: HTTPClientDep) -> LDMFCountriesRepository:
    return LDMFCountriesRepository(http_client, str(settings.ldmf_countries_api.base_url))


LDMFCountriesRepositoryDep = Annotated[LDMFCountriesRepository, Depends(get_ldmf_countries_repository)]


def get_extracted_data_repository(
    db_session: DbSessionDep, conversation_repository: ConversationRepositoryDep
) -> ExtractedDataRepository:
    return ExtractedDataRepository(db_session=db_session, conversation_repository=conversation_repository)


ExtractedDataRepositoryDep = Annotated[ExtractedDataRepository, Depends(get_extracted_data_repository)]


async def get_document_queue_repository() -> DocumentQueueRepository:
    return DocumentQueueRepository(
        settings.document_queue.connection_string,
        settings.document_queue.content_queue_name,
    )


DocumentQueueRepositoryDep = Annotated[DocumentQueueRepository, Depends(get_document_queue_repository)]


def get_industry_repository(http_client: HTTPClientDep) -> IndustryRepository:
    return IndustryRepository(http_client)


IndustryRepositoryDep = Annotated[IndustryRepository, Depends(get_industry_repository)]


def get_role_repository(http_client: HTTPClientDep) -> RoleRepository:
    return RoleRepository(http_client)


RoleRepositoryDep = Annotated[RoleRepository, Depends(get_role_repository)]


def get_service_repository(http_client: HTTPClientDep) -> ServiceRepository:
    return ServiceRepository(http_client)


ServiceRepositoryDep = Annotated[ServiceRepository, Depends(get_service_repository)]
