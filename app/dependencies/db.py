from typing import Annotated, AsyncGenerator, cast

from fastapi import Depends
from sqlalchemy.engine.base import Engine
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

from config import settings


__all__ = ['DbSessionDep', 'async_session_local']


async_engine = create_async_engine(
    settings.db.uri,
    echo=False,
    pool_pre_ping=True,
    pool_size=10,
    max_overflow=20,
    pool_recycle=360,  # every 6 mins
)

async_session_local = sessionmaker(
    bind=cast(Engine, async_engine),
    class_=AsyncSession,
    expire_on_commit=False,
    autocommit=False,
    autoflush=False,
)


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    async with cast(AsyncSession, async_session_local()) as session:
        try:
            yield session
            await session.commit()
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


DbSessionDep = Annotated[AsyncSession, Depends(get_db)]
