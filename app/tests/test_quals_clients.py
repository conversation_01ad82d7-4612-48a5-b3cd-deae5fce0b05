"""Tests for Quals Clients repository."""

from typing import <PERSON><PERSON><PERSON>enerator
from unittest.mock import AsyncMock, MagicMock

import pytest

from config import settings
from core.http_client import CustomAsyncClient
from repositories import QualsClientsRepository
from schemas import (
    ClientCreateRequest,
    ClientSearchRequest,
)


class TestQualsClientsRepository:
    """Test cases for QualsClientsRepository."""

    @pytest.fixture
    async def http_client(self) -> AsyncGenerator[CustomAsyncClient, None]:
        """Create HTTP client for testing."""
        async with CustomAsyncClient(settings=settings) as client:
            yield client

    @pytest.fixture
    def repository(self, http_client: CustomAsyncClient) -> QualsClientsRepository:
        """Create repository instance for testing."""
        return QualsClientsRepository(http_client)

    async def test_search_clients_mock(self, repository: QualsClientsRepository):
        """Test client search with mock data."""
        # Arrange
        search_request = ClientSearchRequest(contains='test', page_size=10, page_idx=0)

        # Act
        response = await repository.search_clients(search_request, token='test_token')

        # Assert
        assert response.clients is not None
        assert len(response.clients) <= search_request.page_size
        assert response.page_size == search_request.page_size
        assert response.page_idx == search_request.page_idx
        assert response.total_count >= len(response.clients)

        # Check that client names contain the search term
        for client in response.clients:
            assert search_request.contains.lower() in client.name.lower()
            assert client.id is not None
            assert client.quals_count >= 0
            assert client.client_confidentiality is not None

    async def test_create_client_mock(self, repository: QualsClientsRepository):
        """Test client creation with mock data."""
        # Arrange
        create_request = ClientCreateRequest(name='Test Client Inc')

        # Act
        response = await repository.create_client(create_request, token='test_token')

        # Assert
        assert response.success
        assert response.client is not None
        assert response.client.name == create_request.name
        assert response.client.id is not None
        assert response.client.description is not None
        assert response.message is not None

        # Check that the response has the expected structure
        assert response.client.primary_local_industry is not None
        assert response.client.primary_global_industry is not None
        assert isinstance(response.client.secondary_local_industries, list)
        assert isinstance(response.client.secondary_global_industries, list)

    async def test_search_clients_pagination(self, repository: QualsClientsRepository):
        """Test client search pagination."""
        # Arrange
        search_request_page_0 = ClientSearchRequest(contains='inc', page_size=2, page_idx=0)
        search_request_page_1 = ClientSearchRequest(contains='inc', page_size=2, page_idx=1)

        # Act
        response_page_0 = await repository.search_clients(search_request_page_0, token='test_token')
        response_page_1 = await repository.search_clients(search_request_page_1, token='test_token')

        # Assert
        assert response_page_0.page_idx == 0
        assert response_page_1.page_idx == 1
        assert len(response_page_0.clients) <= 2
        assert len(response_page_1.clients) <= 2

        # Ensure different pages return different results (if available)
        if len(response_page_0.clients) > 0 and len(response_page_1.clients) > 0:
            page_0_ids = {client.id for client in response_page_0.clients}
            page_1_ids = {client.id for client in response_page_1.clients}
            # Pages should not have overlapping client IDs
            assert page_0_ids.isdisjoint(page_1_ids)

    async def test_search_clients_empty_results(self, repository: QualsClientsRepository):
        """Test client search with query that should return no results."""
        # Arrange
        search_request = ClientSearchRequest(contains='nonexistentclientname12345', page_size=10, page_idx=0)

        # Act
        response = await repository.search_clients(search_request, token='test_token')

        # Assert
        assert response.clients is not None
        assert isinstance(response.clients, list)
        assert response.page_size == search_request.page_size
        assert response.page_idx == search_request.page_idx

    async def test_create_client_with_special_characters(self, repository: QualsClientsRepository):
        """Test client creation with special characters in name."""
        # Arrange
        create_request = ClientCreateRequest(name='Test & Associates Ltd.')

        # Act
        response = await repository.create_client(create_request, token='test_token')

        # Assert
        assert response.success
        assert response.client.name == create_request.name
        assert response.client.id is not None

    async def test_search_clients_real_list_response(self, repository: QualsClientsRepository):
        """Test client search with real implementation returning a list."""
        # Arrange
        settings.quals_clients_api.mock_client_api_enabled = False
        search_request = ClientSearchRequest(contains='test', page_size=10, page_idx=0)
        mock_response = MagicMock()
        mock_response.json.return_value = [
            {'id': '1', 'name': 'Test Client 1', 'qualsCount': 10, 'clientConfidentiality': 1}
        ]
        repository._http_client.get = AsyncMock(return_value=mock_response)

        # Act
        response = await repository.search_clients(search_request, token='test_token')

        # Assert
        assert len(response.clients) == 1
        assert response.clients[0].name == 'Test Client 1'

    async def test_search_clients_real_dict_response(self, repository: QualsClientsRepository):
        """Test client search with real implementation returning a dict."""
        # Arrange
        settings.quals_clients_api.mock_client_api_enabled = False
        search_request = ClientSearchRequest(contains='test', page_size=10, page_idx=0)
        mock_response = MagicMock()
        mock_response.json.return_value = {
            'clients': [{'id': '1', 'name': 'Test Client 1', 'qualsCount': 10, 'clientConfidentiality': 1}],
            'totalCount': 1,
        }
        repository._http_client.get = AsyncMock(return_value=mock_response)

        # Act
        response = await repository.search_clients(search_request, token='test_token')

        # Assert
        assert len(response.clients) == 1
        assert response.clients[0].name == 'Test Client 1'
        assert response.total_count == 1

    async def test_search_clients_real_error(self, repository: QualsClientsRepository):
        """Test client search with real implementation raising an error."""
        # Arrange
        settings.quals_clients_api.mock_client_api_enabled = False
        search_request = ClientSearchRequest(contains='test', page_size=10, page_idx=0)
        repository._http_client.get = AsyncMock(side_effect=Exception('API Error'))

        # Act & Assert
        with pytest.raises(Exception, match='API Error'):
            await repository.search_clients(search_request, token='test_token')

    async def test_create_client_real_error(self, repository: QualsClientsRepository):
        """Test client creation with real implementation raising an error."""
        # Arrange
        settings.quals_clients_api.mock_client_api_enabled = False
        create_request = ClientCreateRequest(name='Test Client Inc')
        repository._http_client.post = AsyncMock(side_effect=Exception('API Error'))

        # Act & Assert
        with pytest.raises(Exception, match='API Error'):
            await repository.create_client(create_request, token='test_token')

    async def test_create_client_real_success(self, repository: QualsClientsRepository):
        """Test client creation with real implementation returning a dict."""
        # Arrange
        settings.quals_clients_api.mock_client_api_enabled = False
        create_request = ClientCreateRequest(name='Test Client Inc')
        mock_response = MagicMock()
        mock_response.json.return_value = {
            'id': '1',
            'name': 'Test Client Inc',
            'description': 'description',
            'primaryLocalIndustry': 'primaryLocalIndustry',
            'primaryGlobalIndustry': 'primaryGlobalIndustry',
            'secondaryLocalIndustries': [],
            'secondaryGlobalIndustries': [],
        }
        repository._http_client.post = AsyncMock(return_value=mock_response)

        # Act
        response = await repository.create_client(create_request, token='test_token')

        # Assert
        assert response.success
        assert response.client.name == 'Test Client Inc'
