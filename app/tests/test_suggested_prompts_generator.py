from datetime import datetime
from unittest.mock import AsyncMock
from uuid import UUID

import pytest

from constants.message import ConversationMessageIntention, MessageRole, MessageType, SystemReplyType
from schemas import AggregatedData, ConfirmedData, ConversationData
from schemas.conversation_message.message import UserMessageSerializer
from services.suggestions import SuggestedPromptsGenerator, SuggestedUserPrompt


class TestSuggestedPromptsGenerator:
    @pytest.mark.asyncio
    async def test_dash_discard_message_intention(self):
        suggested_prompts_generator = SuggestedPromptsGenerator(
            conversation_id=UUID('00000000-0000-0000-0000-000000000000'),
            conversation_data=ConversationData(
                conversation_message_history=[],
                confirmed_data=AsyncMock(),
                aggregated_data=AsyncMock(),
                conversation=AsyncMock(),
            ),
            user_message=UserMessageSerializer(
                id=UUID('00000000-0000-0000-0000-000000000000'),
                conversation_id=UUID('00000000-0000-0000-0000-000000000000'),
                role=MessageRole.USER,
                type=MessageType.TEXT,
                content='test',
                created_at=datetime.now(),
                selected_option=None,
            ),
            intention=ConversationMessageIntention.DASH_DISCARD,
            conversation_message_history=[],
            current_reply_type=SystemReplyType.EMPTY,
            called_from=self.test_dash_discard_message_intention.__name__,
        )

        suggested_prompts = await suggested_prompts_generator.run()

        assert suggested_prompts == [SuggestedUserPrompt.UPLOAD_DOCUMENT, SuggestedUserPrompt.WRITE_A_BRIEF_DESCRIPTION]


@pytest.mark.asyncio
async def test_no_create_my_qual_suggestion_when_all_fields_complete():
    """Test that 'No, create my qual' is suggested when all required fields are complete."""
    confirmed_data = ConfirmedData(
        client_name='Test Client',
        ldmf_country='USA',
        date_intervals=('2023-01-01', '2023-12-31'),
        objective_and_scope='Scope',
        outcomes='Outcome',
    )
    aggregated_data = AggregatedData(
        client_name=['Test Client'],
        ldmf_country=['USA'],
        date_intervals=[('2023-01-01', '2023-12-31')],
        objective_and_scope='Scope',
        outcomes='Outcome',
    )
    conversation = AsyncMock()
    conversation.State = 'initial'
    conversation_data = ConversationData(
        conversation_message_history=[],
        confirmed_data=confirmed_data,
        aggregated_data=aggregated_data,
        conversation=conversation,
    )
    user_message = UserMessageSerializer(
        id=UUID('00000000-0000-0000-0000-000000000000'),
        conversation_id=UUID('00000000-0000-0000-0000-000000000000'),
        role=MessageRole.USER,
        type=MessageType.TEXT,
        content='test',
        created_at=datetime.now(),
        selected_option=None,
    )
    generator = SuggestedPromptsGenerator(
        conversation_id=UUID('00000000-0000-0000-0000-000000000000'),
        conversation_data=conversation_data,
        conversation_message_history=conversation_data.conversation_message_history,
        user_message=user_message,
        intention=ConversationMessageIntention.UNDEFINED,
        current_reply_type=SystemReplyType.CONFIRMED_FIELDS_READY,
        called_from='test_no_create_my_qual_suggestion_when_all_fields_complete',
    )
    prompts = await generator.run()
    assert SuggestedUserPrompt.NO_CREATE_MY_QUAL in prompts


@pytest.mark.asyncio
async def test_no_create_my_qual_not_suggested_when_fields_incomplete():
    """Test that 'No, create my qual' is NOT suggested when required fields are incomplete."""
    confirmed_data = ConfirmedData(
        client_name='Test Client',
        ldmf_country=None,  # Incomplete
        date_intervals=None,
        objective_and_scope=None,
        outcomes=None,
    )
    aggregated_data = AggregatedData(
        client_name=['Test Client'],
        ldmf_country=[],
        date_intervals=[],
        objective_and_scope=None,
        outcomes=None,
    )
    conversation = AsyncMock()
    conversation.State = 'initial'
    conversation_data = ConversationData(
        conversation_message_history=[],
        confirmed_data=confirmed_data,
        aggregated_data=aggregated_data,
        conversation=conversation,
    )
    user_message = UserMessageSerializer(
        id=UUID('00000000-0000-0000-0000-000000000000'),
        conversation_id=UUID('00000000-0000-0000-0000-000000000000'),
        role=MessageRole.USER,
        type=MessageType.TEXT,
        content='test',
        created_at=datetime.now(),
        selected_option=None,
    )
    generator = SuggestedPromptsGenerator(
        conversation_id=UUID('00000000-0000-0000-0000-000000000000'),
        conversation_data=conversation_data,
        conversation_message_history=conversation_data.conversation_message_history,
        user_message=user_message,
        intention=ConversationMessageIntention.UNDEFINED,
        current_reply_type=SystemReplyType.CONFIRMED_FIELDS_READY,
        called_from='test_no_create_my_qual_not_suggested_when_fields_incomplete',
    )
    prompts = await generator.run()
    assert SuggestedUserPrompt.NO_CREATE_MY_QUAL not in prompts


@pytest.mark.asyncio
async def test_enter_a_new_client_suggestion_for_multiple_client_names():
    """Test that 'Enter a new client' is suggested when multiple client name variations are detected."""
    confirmed_data = ConfirmedData(client_name=None)
    aggregated_data = AggregatedData(client_name=['A', 'B'])
    conversation = AsyncMock()
    conversation.State = 'initial'
    conversation_data = ConversationData(
        conversation_message_history=[],
        confirmed_data=confirmed_data,
        aggregated_data=aggregated_data,
        conversation=conversation,
    )
    user_message = UserMessageSerializer(
        id=UUID('00000000-0000-0000-0000-000000000000'),
        conversation_id=UUID('00000000-0000-0000-0000-000000000000'),
        role=MessageRole.USER,
        type=MessageType.TEXT,
        content='test',
        created_at=datetime.now(),
        selected_option=None,
    )
    generator = SuggestedPromptsGenerator(
        conversation_id=UUID('00000000-0000-0000-0000-000000000000'),
        conversation_data=conversation_data,
        conversation_message_history=conversation_data.conversation_message_history,
        user_message=user_message,
        intention=ConversationMessageIntention.UNDEFINED,
        current_reply_type=SystemReplyType.CLIENT_NAME_MULTIPLE_OPTIONS,
        called_from='test_enter_a_new_client_suggestion_for_multiple_client_names',
    )
    prompts = await generator.run()
    assert SuggestedUserPrompt.ENTER_A_NEW_CLIENT in prompts


@pytest.mark.asyncio
async def test_yes_this_is_correct_suggestion_for_single_client_name():
    """Test that 'Yes, this is correct' is suggested when a single client name is detected."""
    confirmed_data = ConfirmedData(client_name=None)
    aggregated_data = AggregatedData(client_name=['A'])
    conversation = AsyncMock()
    conversation.State = 'initial'
    conversation_data = ConversationData(
        conversation_message_history=[],
        confirmed_data=confirmed_data,
        aggregated_data=aggregated_data,
        conversation=conversation,
    )
    user_message = UserMessageSerializer(
        id=UUID('00000000-0000-0000-0000-000000000000'),
        conversation_id=UUID('00000000-0000-0000-0000-000000000000'),
        role=MessageRole.USER,
        type=MessageType.TEXT,
        content='test',
        created_at=datetime.now(),
        selected_option=None,
    )
    generator = SuggestedPromptsGenerator(
        conversation_id=UUID('00000000-0000-0000-0000-000000000000'),
        conversation_data=conversation_data,
        conversation_message_history=conversation_data.conversation_message_history,
        user_message=user_message,
        intention=ConversationMessageIntention.UNDEFINED,
        current_reply_type=SystemReplyType.CLIENT_NAME_SINGLE_CONFIRMATION,
        called_from='test_yes_this_is_correct_suggestion_for_single_client_name',
    )
    prompts = await generator.run()
    assert SuggestedUserPrompt.YES_THIS_IS_CORRECT in prompts
