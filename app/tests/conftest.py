from typing import AsyncGenerator

from httpx import ASGITransport
import pytest
import pytest_asyncio

from config import settings
from constants.environment import Environment
from core.http_client import CustomAsyncClient
from core.urls import URLResolver
from main import app

from .fixtures import *  # noqa: F403


if settings.environment != Environment.TEST:
    raise RuntimeError('Tests are allowed to be run in the "test" environment only')


@pytest_asyncio.fixture()
async def async_client() -> AsyncGenerator[CustomAsyncClient, None]:
    async with CustomAsyncClient(
        transport=ASGITransport(app=app),
        base_url='http://test',
        raise_for_status=False,
        settings=settings,
    ) as client:
        yield client


@pytest.fixture(scope='session')
def url_resolver():
    """Fixture that provides URL resolver for tests."""
    return URLResolver(app)


def pytest_collection_modifyitems(items):
    session_scope_marker = pytest.mark.asyncio(loop_scope='session')
    for async_test in (item for item in items if pytest_asyncio.is_async_test(item)):
        async_test.add_marker(session_scope_marker, append=False)
