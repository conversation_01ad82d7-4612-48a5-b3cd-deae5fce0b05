from datetime import date
from unittest.mock import AsyncMock, patch
from uuid import uuid4

from fastapi import status
import pytest

from constants.extracted_data import ConversationState, DataSourceType, FieldStatus, MissingDataStatus
from constants.message import NEED_INFO_OUTCOMES, OUTCOMES_AGGREGATED_QUESTION, UNDEFINED_REPLY
from constants.operation_ids import operation_ids
from core.http_client import CustomAsyncClient
from schemas import AggregatedData, ExtractedData
from schemas.confirmed_data import ConfirmedData
from services import ExtractedDataService
from services.extracted_data.handlers import OutcomesHandler


class TestOutcomesHandlerUnit:
    """Unit tests for OutcomesHandler specific functionality."""

    @pytest.fixture
    def outcomes_handler(self):
        """Fixture providing OutcomesHandler instance."""
        return OutcomesHandler()

    @pytest.fixture
    def empty_aggregated_data(self):
        """Fixture providing empty AggregatedData."""
        return AggregatedData()

    @pytest.fixture
    def aggregated_data_with_outcomes(self):
        """Fixture providing AggregatedData with outcomes."""
        return AggregatedData(outcomes='Test outcomes from aggregated data')

    @pytest.fixture
    def empty_confirmed_data(self):
        """Fixture providing empty ConfirmedData."""
        return ConfirmedData()

    @pytest.fixture
    def confirmed_data_with_outcomes(self):
        """Fixture providing ConfirmedData with outcomes."""
        return ConfirmedData(outcomes='Confirmed test outcomes')

    async def test_outcomes_already_confirmed(
        self, outcomes_handler, empty_aggregated_data, confirmed_data_with_outcomes
    ):
        """Test when outcomes are already confirmed - should return CONFIRMED status."""
        response = await outcomes_handler.check_and_get_response(
            aggregated_data=empty_aggregated_data, confirmed_data=confirmed_data_with_outcomes
        )

        assert not response.needs_confirmation
        assert response.system_message is None
        assert response.next_expected_field is None
        assert response.field_status == FieldStatus.CONFIRMED

    async def test_outcomes_in_aggregated_data_not_confirmed(
        self, outcomes_handler, aggregated_data_with_outcomes, empty_confirmed_data
    ):
        """Test when outcomes exist in aggregated_data but not confirmed - should return SINGLE status."""
        response = await outcomes_handler.check_and_get_response(
            aggregated_data=aggregated_data_with_outcomes, confirmed_data=empty_confirmed_data
        )

        assert response.needs_confirmation
        assert response.field_status == FieldStatus.SINGLE
        assert response.system_message == OUTCOMES_AGGREGATED_QUESTION.format(
            outcomes=aggregated_data_with_outcomes.outcomes
        )
        assert response.next_expected_field is None

    async def test_outcomes_missing_entirely(self, outcomes_handler, empty_aggregated_data, empty_confirmed_data):
        """Test when outcomes are missing entirely - should return MISSING status."""
        response = await outcomes_handler.check_and_get_response(
            aggregated_data=empty_aggregated_data, confirmed_data=empty_confirmed_data
        )

        assert response.needs_confirmation
        assert response.field_status == FieldStatus.MISSING
        assert response.system_message == NEED_INFO_OUTCOMES
        assert response.next_expected_field is None

    @pytest.mark.asyncio
    async def test_confirmed_data_overrides_aggregated_data(
        self, outcomes_handler, aggregated_data_with_outcomes, confirmed_data_with_outcomes
    ):
        """Test that confirmed data takes precedence over aggregated data."""
        response = await outcomes_handler.check_and_get_response(
            aggregated_data=aggregated_data_with_outcomes, confirmed_data=confirmed_data_with_outcomes
        )

        # Should return CONFIRMED status even though aggregated data also has outcomes
        assert not response.needs_confirmation
        assert response.system_message is None
        assert response.next_expected_field is None
        assert response.field_status == FieldStatus.CONFIRMED


class TestExtractedDataServiceIntegration:
    """Integration tests for ExtractedDataService with real database connections."""

    @pytest.fixture
    def mock_data_services(self):
        return {
            'industry_data_service': AsyncMock(extract=AsyncMock(return_value={'ClientIndustries': ['Industry1']})),
            'role_data_service': AsyncMock(extract=AsyncMock(return_value={'ClientRoles': ['Role1']})),
            'service_data_service': AsyncMock(extract=AsyncMock(return_value={'ClientServices': ['Service1']})),
        }

    @pytest.fixture
    def extracted_data_service_test(self, mock_data_services):
        return ExtractedDataService(extracted_data_repository=AsyncMock(), **mock_data_services)

    @pytest.mark.asyncio
    async def test_missing_data_detection_outcomes_missing(
        self,
        test_conversation_id,
        extracted_data_service_real,
    ):
        """Test missing data detection when outcomes are completely missing."""
        # Test with no extracted data at all
        confirmed_data = ConfirmedData()

        response = await extracted_data_service_real.get_missing_required_data_prompts(
            conversation_id=test_conversation_id, confirmed_data=confirmed_data, token='test_token'
        )

        # Should detect missing client info first (based on field collection order)
        assert response.status == MissingDataStatus.MISSING_DATA
        assert response.conversation_state == ConversationState.COLLECTING_CLIENT_NAME
        assert response.message is not None
        assert len(response.missing_fields) > 0

    async def test_missing_data_detection_outcomes_in_aggregated_data(
        self,
        test_conversation_id,
        extracted_data_service_real,
        extracted_data_repository_real,
    ):
        """Test missing data detection when outcomes exist in aggregated data but need confirmation."""
        # Create extracted data with outcomes
        extracted_data = ExtractedData.create(
            conversation_id=test_conversation_id, data_source_type=DataSourceType.KX_DASH
        )
        extracted_data.outcomes = 'Test outcomes from KX Dash'
        extracted_data.client_name = ['Test Client']
        extracted_data.ldmf_country = ['Test Country']
        extracted_data.start_date = date(2023, 1, 1)
        extracted_data.end_date = date(2023, 12, 31)
        extracted_data.objective_and_scope = 'Test objective'

        # Save to database
        await extracted_data_repository_real.update(extracted_data)

        # Test with confirmed data for all fields except outcomes
        confirmed_data = ConfirmedData(
            client_name='Test Client',
            ldmf_country='Test Country',
            date_intervals=('2023-01-01', '2023-12-31'),
            objective_and_scope='Test objective',
            # outcomes intentionally left None
        )

        response = await extracted_data_service_real.get_missing_required_data_prompts(
            conversation_id=test_conversation_id, confirmed_data=confirmed_data, token='test_token'
        )

        # Should detect outcomes need confirmation
        assert response.status == MissingDataStatus.MISSING_DATA
        assert response.conversation_state == ConversationState.COLLECTING_OUTCOMES
        assert OUTCOMES_AGGREGATED_QUESTION.format(outcomes=extracted_data.outcomes) in response.message
        assert 'outcomes' in response.missing_fields

    async def test_missing_data_detection_outcomes_confirmed(
        self,
        test_conversation_id,
        extracted_data_service_real,
        extracted_data_repository_real,
    ):
        """Test missing data detection when outcomes are confirmed."""
        # Create extracted data with outcomes
        extracted_data = ExtractedData.create(
            conversation_id=test_conversation_id, data_source_type=DataSourceType.KX_DASH
        )
        extracted_data.outcomes = 'Test outcomes from KX Dash'
        extracted_data.client_name = ['Test Client']
        extracted_data.ldmf_country = ['Test Country']
        extracted_data.start_date = date(2023, 1, 1)
        extracted_data.end_date = date(2023, 12, 31)
        extracted_data.objective_and_scope = 'Test objective'

        # Save to database
        await extracted_data_repository_real.update(extracted_data)

        # Test with all fields confirmed including outcomes
        confirmed_data = ConfirmedData(
            client_name='Test Client',
            ldmf_country='Test Country',
            date_intervals=('2023-01-01', '2023-12-31'),
            objective_and_scope='Test objective',
            outcomes='Confirmed test outcomes',
        )

        response = await extracted_data_service_real.get_missing_required_data_prompts(
            conversation_id=test_conversation_id, confirmed_data=confirmed_data, token='test_token'
        )

        # Should indicate data is complete
        assert response.status == MissingDataStatus.DATA_COMPLETE
        assert response.conversation_state == ConversationState.DATA_COMPLETE
        assert len(response.missing_fields) == 0

    async def test_progressive_data_collection_flow(
        self,
        test_conversation_id,
        extracted_data_service_real,
        extracted_data_repository_real,
    ):
        """Test progressive data collection across multiple interactions."""
        # Create extracted data with some fields
        extracted_data = ExtractedData.create(
            conversation_id=test_conversation_id, data_source_type=DataSourceType.DOCUMENTS
        )
        extracted_data.client_name = ['Progressive Client']
        extracted_data.outcomes = 'Progressive outcomes'

        # Save to database
        await extracted_data_repository_real.update(extracted_data)

        # Step 1: Confirm client name
        confirmed_data = ConfirmedData()
        confirmed_data.client_name = 'Progressive Client'
        response = await extracted_data_service_real.get_missing_required_data_prompts(
            conversation_id=test_conversation_id, confirmed_data=confirmed_data, token='test_token'
        )

        # Should ask for country next
        assert response.status == MissingDataStatus.MISSING_DATA
        assert response.conversation_state == ConversationState.COLLECTING_COUNTRY

        # Step 2: Confirm country
        confirmed_data.ldmf_country = 'Progressive Country'
        response = await extracted_data_service_real.get_missing_required_data_prompts(
            conversation_id=test_conversation_id, confirmed_data=confirmed_data, token='test_token'
        )

        # Should ask for dates next
        assert response.status == MissingDataStatus.MISSING_DATA
        assert response.conversation_state == ConversationState.COLLECTING_DATES

        # Step 3: Confirm dates
        confirmed_data.date_intervals = ('2023-01-01', '2023-12-31')
        response = await extracted_data_service_real.get_missing_required_data_prompts(
            conversation_id=test_conversation_id, confirmed_data=confirmed_data, token='test_token'
        )

        # Should ask for objective next
        assert response.status == MissingDataStatus.MISSING_DATA
        assert response.conversation_state == ConversationState.COLLECTING_OBJECTIVE

        # Step 4: Confirm objective
        confirmed_data.objective_and_scope = 'Progressive objective'
        response = await extracted_data_service_real.get_missing_required_data_prompts(
            conversation_id=test_conversation_id, confirmed_data=confirmed_data, token='test_token'
        )

        # Should ask for outcomes confirmation (since it exists in aggregated data)
        assert response.status == MissingDataStatus.MISSING_DATA
        assert response.conversation_state == ConversationState.COLLECTING_OUTCOMES
        assert OUTCOMES_AGGREGATED_QUESTION.format(outcomes=extracted_data.outcomes) in response.message

        # Step 5: Confirm outcomes
        confirmed_data.outcomes = 'Progressive outcomes'
        response = await extracted_data_service_real.get_missing_required_data_prompts(
            conversation_id=test_conversation_id, confirmed_data=confirmed_data, token='test_token'
        )

        # Should indicate data is complete
        assert response.status == MissingDataStatus.DATA_COMPLETE
        assert response.conversation_state == ConversationState.DATA_COMPLETE


class TestEndToEndMessageFlow:
    """End-to-end integration tests for the complete message flow."""

    async def test_uncertain_intent_triggers_missing_data_detection(
        self,
        auth_mock,
        auth_header,
        async_client: CustomAsyncClient,
        url_resolver,
        test_conversation_id,
        extracted_data_repository_real_with_autocommit,
    ):
        """Test that uncertain intent triggers missing data detection flow."""
        # Create some extracted data with outcomes
        extracted_data = ExtractedData.create(
            conversation_id=test_conversation_id, data_source_type=DataSourceType.KX_DASH
        )
        extracted_data.outcomes = 'Test outcomes from integration'
        extracted_data.client_name = ['Integration Client']
        extracted_data.ldmf_country = ['Integration Country']
        extracted_data.start_date = date(2023, 1, 1)
        extracted_data.end_date = date(2023, 12, 31)
        extracted_data.objective_and_scope = 'Integration objective'

        # Save to database using a fresh session and autocommit
        await extracted_data_repository_real_with_autocommit.update(extracted_data)

        # Send a user message
        message_url = url_resolver.reverse(operation_ids.message.CREATE)
        message_data = {
            'conversation_id': str(test_conversation_id),
            'content': 'I need help with my qual',
        }

        with patch(
            'repositories.ldmf_countries.LDMFCountriesRepository.list',
            new_callable=AsyncMock,
            return_value=[],
        ):
            response = await async_client.post(message_url, headers=auth_header, data=message_data)

        assert response.status_code == status.HTTP_201_CREATED, response.json()
        data = response.json()

        # Verify the system response contains missing data guidance
        assert 'system' in data
        system_data = data['system']
        # The system message should contain the undefined reply plus enriched proactive chat content
        assert UNDEFINED_REPLY in system_data['content']
        # No need to add proactive chat content when reply is UNDEFINED
        assert 'Please confirm the Client Name:' not in system_data['content']

    async def test_conversation_state_tracking(
        self,
        auth_mock,
        auth_header,
        async_client: CustomAsyncClient,
        url_resolver,
        test_conversation_id,
        conversation_repository_dep,
        extracted_data_repository_real_with_autocommit,
    ):
        """Test conversation state tracking during missing data collection."""
        # Create extracted data with only outcomes
        extracted_data = ExtractedData.create(
            conversation_id=test_conversation_id, data_source_type=DataSourceType.PROMPT
        )
        extracted_data.outcomes = 'State tracking outcomes'

        # Save to database using a fresh session and autocommit
        await extracted_data_repository_real_with_autocommit.update(extracted_data)

        # Send a user message
        message_url = url_resolver.reverse(operation_ids.message.CREATE)
        message_data = {
            'conversation_id': str(test_conversation_id),
            'content': 'Help me with missing data',
        }

        response = await async_client.post(message_url, headers=auth_header, data=message_data)

        assert response.status_code == status.HTTP_201_CREATED, response.json()

        # Check that conversation state was updated using a fresh session
        conversation = await conversation_repository_dep.get(test_conversation_id)
        assert conversation is not None
        # State should be updated to reflect the first missing field (client name)
        assert conversation.State == ConversationState.COLLECTING_CLIENT_NAME

    async def test_confirmed_data_storage_and_retrieval(
        self,
        test_conversation_id,
        conversation_repository_dep,
    ):
        """Test confirmed data JSON field storage and retrieval."""
        # Create test confirmed data
        confirmed_data = ConfirmedData(
            client_name='Storage Test Client', ldmf_country='Storage Test Country', outcomes='Storage Test Outcomes'
        )

        # Update conversation with confirmed data
        await conversation_repository_dep.update_confirmed_data_and_state(
            public_id=test_conversation_id, confirmed_data=confirmed_data, state=ConversationState.COLLECTING_OUTCOMES
        )

        # Retrieve confirmed data
        retrieved_data = await conversation_repository_dep.get_confirmed_data(test_conversation_id)

        # Verify data was stored and retrieved correctly
        assert retrieved_data.client_name == confirmed_data.client_name
        assert retrieved_data.ldmf_country == confirmed_data.ldmf_country
        assert retrieved_data.outcomes == confirmed_data.outcomes
        assert retrieved_data.date_intervals is None
        assert retrieved_data.objective_and_scope is None


class TestThreeScenarioHandling:
    """Test the three-scenario handling pattern (zero/one/multiple values) for outcomes field."""

    async def test_zero_values_scenario(
        self,
        test_conversation_id,
        extracted_data_service_real,
    ):
        """Test scenario with zero outcomes values."""
        # No extracted data created - zero values scenario
        confirmed_data = ConfirmedData(
            client_name='Test Client',
            ldmf_country='Test Country',
            date_intervals=('2023-01-01', '2023-12-31'),
            objective_and_scope='Test objective',
            # outcomes left None
        )

        response = await extracted_data_service_real.get_missing_required_data_prompts(
            conversation_id=test_conversation_id, confirmed_data=confirmed_data, token='test_token'
        )

        # Should detect missing outcomes
        assert response.status == MissingDataStatus.MISSING_DATA
        assert response.conversation_state == ConversationState.COLLECTING_OUTCOMES
        assert response.message == NEED_INFO_OUTCOMES

    async def test_one_value_scenario(
        self,
        test_conversation_id,
        extracted_data_service_real,
        extracted_data_repository_real,
    ):
        """Test scenario with one outcomes value."""
        # Create extracted data with single outcomes value
        extracted_data = ExtractedData.create(
            conversation_id=test_conversation_id, data_source_type=DataSourceType.KX_DASH
        )
        extracted_data.outcomes = 'Single outcomes value'

        # Save to database
        await extracted_data_repository_real.update(extracted_data)

        confirmed_data = ConfirmedData(
            client_name='Test Client',
            ldmf_country='Test Country',
            date_intervals=('2023-01-01', '2023-12-31'),
            objective_and_scope='Test objective',
            # outcomes left None for confirmation
        )

        response = await extracted_data_service_real.get_missing_required_data_prompts(
            conversation_id=test_conversation_id, confirmed_data=confirmed_data, token='test_token'
        )

        # Should ask for confirmation of single value
        assert response.status == MissingDataStatus.MISSING_DATA
        assert response.conversation_state == ConversationState.COLLECTING_OUTCOMES
        assert OUTCOMES_AGGREGATED_QUESTION.format(outcomes=extracted_data.outcomes) in response.message

    async def test_multiple_values_scenario_priority_override(
        self,
        test_conversation_id,
        extracted_data_service_real,
        extracted_data_repository_real,
    ):
        """Test scenario with multiple outcomes values - priority override behavior."""
        # Create extracted data from multiple sources with different outcomes
        kx_dash_data = ExtractedData.create(
            conversation_id=test_conversation_id, data_source_type=DataSourceType.KX_DASH
        )
        kx_dash_data.outcomes = 'KX Dash outcomes'
        await extracted_data_repository_real.update(kx_dash_data)

        documents_data = ExtractedData.create(
            conversation_id=test_conversation_id, data_source_type=DataSourceType.DOCUMENTS
        )
        documents_data.outcomes = 'Documents outcomes'
        await extracted_data_repository_real.update(documents_data)

        prompt_data = ExtractedData.create(conversation_id=test_conversation_id, data_source_type=DataSourceType.PROMPT)
        prompt_data.outcomes = 'Prompt outcomes'
        await extracted_data_repository_real.update(prompt_data)

        confirmed_data = ConfirmedData(
            client_name='Test Client',
            ldmf_country='Test Country',
            date_intervals=('2023-01-01', '2023-12-31'),
            objective_and_scope='Test objective',
            # outcomes left None for confirmation
        )

        response = await extracted_data_service_real.get_missing_required_data_prompts(
            conversation_id=test_conversation_id, confirmed_data=confirmed_data, token='test_token'
        )

        # Should ask for confirmation of highest priority value (KX_DASH)
        assert response.status == MissingDataStatus.MISSING_DATA
        assert response.conversation_state == ConversationState.COLLECTING_OUTCOMES
        assert OUTCOMES_AGGREGATED_QUESTION.format(outcomes=prompt_data.outcomes) in response.message


class TestErrorHandlingAndEdgeCases:
    """Test error handling and edge cases."""

    async def test_nonexistent_conversation_id(
        self,
        extracted_data_service_real,
    ):
        """Test handling of nonexistent conversation ID."""
        nonexistent_id = uuid4()
        confirmed_data = ConfirmedData()

        # Should not raise exception, should handle gracefully
        response = await extracted_data_service_real.get_missing_required_data_prompts(
            conversation_id=nonexistent_id, confirmed_data=confirmed_data, token='test_token'
        )

        # Should detect missing data (since no extracted data exists)
        assert response.status == MissingDataStatus.MISSING_DATA
        assert response.conversation_state == ConversationState.COLLECTING_CLIENT_NAME

    async def test_malformed_confirmed_data_json(self):
        """Test handling of malformed confirmed data JSON."""
        # Test the ConfirmedData.from_json_string method directly

        # Test with malformed JSON
        malformed_json = '{"client_name": "test", "invalid_json":'
        confirmed_data = ConfirmedData.from_json_string(malformed_json)

        # Should return empty ConfirmedData object
        assert confirmed_data.client_name is None
        assert confirmed_data.outcomes is None

        # Test with None
        confirmed_data = ConfirmedData.from_json_string(None)
        assert confirmed_data.client_name is None
        assert confirmed_data.outcomes is None

        # Test with empty string
        confirmed_data = ConfirmedData.from_json_string('')
        assert confirmed_data.client_name is None
        assert confirmed_data.outcomes is None
