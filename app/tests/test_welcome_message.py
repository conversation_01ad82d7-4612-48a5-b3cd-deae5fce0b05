from unittest.mock import AsyncMock
from uuid import UUID

import pytest

from constants.message import (
    WELCOME_MESSAGE,
    WELCOME_MESSAGE_WITH_MANY_DASH_TASKS,
    WELCOME_MESSAGE_WITH_ONE_DASH_TASK,
    MessageRole,
    MessageType,
    SystemReplyType,
)
from schemas.conversation_message import MessageValidator
from services.conversation import ConversationService


class TestWelcomeMessage:
    @pytest.mark.asyncio
    async def test_create_welcome_message_with_multiple_dash_tasks(self):
        mock_conversation_message_service = AsyncMock()
        mock_kx_dash_service = AsyncMock()
        conversation_service = ConversationService(
            conversation_repository=AsyncMock(),
            conversation_message_service=mock_conversation_message_service,
            extracted_data_service=AsyncMock(),
            document_service=AsyncMock(),
            kx_dash_service=mock_kx_dash_service,
        )

        conversation_id = UUID('00000000-0000-0000-0000-000000000000')

        mock_kx_dash_tasks = [
            AsyncMock(activity_id=1, client_name='Client 1', engagement_code='123'),
            AsyncMock(activity_id=2, client_name='Client 2', engagement_code='234'),
        ]
        mock_kx_dash_service.list.return_value = mock_kx_dash_tasks

        mock_message = MessageValidator(
            conversation_id=conversation_id,
            role=MessageRole.SYSTEM,
            type=MessageType.TEXT,
            content=WELCOME_MESSAGE_WITH_MANY_DASH_TASKS,
            options=(),
            suggested_prompts=[],
            system_reply_type=SystemReplyType.WELCOME_MESSAGE_WITH_MANY_DASH_TASKS,
        )
        mock_conversation_message_service.create_message.return_value = mock_message

        result = await conversation_service._create_welcome_message(conversation_id, token='')

        mock_kx_dash_service.list.assert_awaited_once()
        mock_conversation_message_service.create_message.assert_awaited_once()

        assert result.content == WELCOME_MESSAGE_WITH_MANY_DASH_TASKS
        assert result.suggested_prompts == []

    @pytest.mark.asyncio
    async def test_create_welcome_message_with_one_dash_task(self):
        mock_conversation_message_service = AsyncMock()
        mock_kx_dash_service = AsyncMock()
        conversation_service = ConversationService(
            conversation_repository=AsyncMock(),
            conversation_message_service=mock_conversation_message_service,
            extracted_data_service=AsyncMock(),
            document_service=AsyncMock(),
            kx_dash_service=mock_kx_dash_service,
        )

        conversation_id = UUID('00000000-0000-0000-0000-000000000000')

        mock_kx_dash_tasks = [AsyncMock(activity_id=1, client_name='Client 1', engagement_code='123')]
        mock_kx_dash_service.list.return_value = mock_kx_dash_tasks

        mock_message = MessageValidator(
            conversation_id=conversation_id,
            role=MessageRole.SYSTEM,
            type=MessageType.TEXT,
            content=WELCOME_MESSAGE_WITH_ONE_DASH_TASK,
            options=(),
            suggested_prompts=['Upload a document', 'Write a brief description'],
            system_reply_type=SystemReplyType.WELCOME_MESSAGE_WITH_ONE_DASH_TASK,
        )
        mock_conversation_message_service.create_message.return_value = mock_message

        result = await conversation_service._create_welcome_message(conversation_id, token='')

        mock_kx_dash_service.list.assert_awaited_once()
        mock_conversation_message_service.create_message.assert_awaited_once()

        assert result.content == WELCOME_MESSAGE_WITH_ONE_DASH_TASK
        assert result.suggested_prompts == ['Upload a document', 'Write a brief description']

    @pytest.mark.asyncio
    async def test_create_welcome_message_with_one_dash_task_selected(self):
        mock_conversation_message_service = AsyncMock()
        mock_kx_dash_service = AsyncMock()
        conversation_service = ConversationService(
            conversation_repository=AsyncMock(),
            conversation_message_service=mock_conversation_message_service,
            extracted_data_service=AsyncMock(),
            document_service=AsyncMock(),
            kx_dash_service=mock_kx_dash_service,
        )

        conversation_id = UUID('00000000-0000-0000-0000-000000000000')
        dash_activity_id = 1

        # Mock the specific dash task that will be retrieved
        mock_dash_task = AsyncMock(activity_id=1, client_name='Client 1', engagement_code='123')
        mock_kx_dash_service.get.return_value = mock_dash_task

        # Mock the message returned by on_select
        mock_on_select_message = MessageValidator(
            conversation_id=conversation_id,
            role=MessageRole.SYSTEM,
            type=MessageType.TEXT,
            content='Mock dash task selected message',
            system_reply_type=SystemReplyType.WELCOME_MESSAGE_WITH_ONE_DASH_SELECTED,
            suggested_prompts=['No, create a new qual'],
        )
        mock_kx_dash_service.on_select.return_value = mock_on_select_message

        # Mock the final message returned by create_message
        mock_final_message = MessageValidator(
            conversation_id=conversation_id,
            role=MessageRole.SYSTEM,
            type=MessageType.TEXT,
            content='Mock dash task selected message',
            system_reply_type=SystemReplyType.WELCOME_MESSAGE_WITH_ONE_DASH_SELECTED,
            suggested_prompts=['No, create a new qual'],
        )
        mock_conversation_message_service.create_message.return_value = mock_final_message

        result = await conversation_service._create_welcome_message(
            conversation_id, token='', dash_activity_id=dash_activity_id
        )

        # Verify that get was called with the correct activity_id
        mock_kx_dash_service.get.assert_awaited_once_with(dash_activity_id, '')

        # Verify that on_select was called with the correct parameters
        mock_kx_dash_service.on_select.assert_awaited_once()
        call_args = mock_kx_dash_service.on_select.call_args
        assert call_args[0][0].activity_id == 1  # First argument should be the KXDashTaskOption
        assert call_args[0][1] == conversation_id  # Second argument should be conversation_id
        assert call_args[1]['is_welcome_message'] is True  # Keyword argument
        assert call_args[1]['token'] == ''  # Keyword argument

        # Verify that create_message was called
        mock_conversation_message_service.create_message.assert_awaited_once()

        assert result.content == 'Mock dash task selected message'
        assert result.system_reply_type == SystemReplyType.WELCOME_MESSAGE_WITH_ONE_DASH_SELECTED
        assert result.suggested_prompts == ['No, create a new qual']

    @pytest.mark.asyncio
    async def test_create_welcome_message_without_dash_tasks(self):
        mock_conversation_message_service = AsyncMock()
        mock_kx_dash_service = AsyncMock()
        conversation_service = ConversationService(
            conversation_repository=AsyncMock(),
            conversation_message_service=mock_conversation_message_service,
            extracted_data_service=AsyncMock(),
            document_service=AsyncMock(),
            kx_dash_service=mock_kx_dash_service,
        )
        conversation_id = UUID('00000000-0000-0000-0000-000000000000')

        mock_kx_dash_service.list.return_value = []

        mock_message = MessageValidator(
            conversation_id=conversation_id,
            role=MessageRole.SYSTEM,
            type=MessageType.TEXT,
            content=WELCOME_MESSAGE,
            options=(),
            suggested_prompts=[],
            system_reply_type=SystemReplyType.WELCOME_MESSAGE,
        )
        mock_conversation_message_service.create_message.return_value = mock_message

        result = await conversation_service._create_welcome_message(conversation_id, token='')

        mock_kx_dash_service.list.assert_awaited_once()
        mock_conversation_message_service.create_message.assert_awaited_once()

        assert result.content == WELCOME_MESSAGE
        assert result.suggested_prompts == []
