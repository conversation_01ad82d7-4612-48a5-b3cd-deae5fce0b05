# Running Tests

This directory contains tests for the API-KXQualsAIassistant application.

## Prerequisites

1. Make sure you have installed the development dependencies:

   ```
   % pip install -r requirements-dev.txt
   ```

1. Ensure the SQL Server container is running:

   ```
   % docker-compose up -d quals-sqlserver
   ```

1. Ensure the current directory is the `app`:

   ```
   % cd app
   ```

1. Ensure the environment variables are exported before running the tests. For example, to export variables from the
`.env.sample`, run the following:

   ```
   % set -a; source .env.sample; set +a
   ```

## Running Tests

1. To run all the tests:

   ```
   % ENVIRONMENT=test pytest
   ```

1. To run tests from a specific test file:

   ```
   % ENVIRONMENT=test pytest tests/test_message_endpoints.py
   ```

1. To run a specific test function:

   ```
   % ENVIRONMENT=test tests/test_conversation_endpoints.py::test_create_conversation_with_welcome_message
   ```

## Test Database

The tests use a separate test database with the prefix `test_` added to the database name specified in your environment
variables. For example, if your database is named `genai_quals`, the test database will be `test_genai_quals`. The test
database is created automatically when running the tests.

## Test Configuration

Test configuration is managed through:

- `app/.pytest.ini` - general pytest configuration
- `app/.coveragerc` - coverage configuration

## Best Practices

1. Always ensure tests are isolated and don't depend on external state
1. Use fixtures from `conftest.py` for common setup
1. Follow the existing pattern for endpoint tests:
   - Test successful operations
   - Test error cases
   - Test input validation
   - Verify response structures
