from urllib.parse import unquote

import pytest

from core.utils import truncate_filename
from exceptions.document import DocumentValidationError


def test_truncate_filename_no_truncation():
    """
    Test that the filename is not truncated if its length is less than or equal to max_length.
    """
    filename = 'short_name.txt'
    assert truncate_filename(filename, 20) == unquote(filename)
    assert truncate_filename(filename, 14) == unquote(filename)


def test_truncate_filename_with_truncation():
    """
    Test that the filename is truncated correctly with an ellipsis.
    """
    filename = 'this_is_a_very_long_filename_that_needs_to_be_truncated.txt'
    expected_truncated = unquote('this_is_a_very_lo') + '...'
    assert truncate_filename(filename, 20) == expected_truncated


def test_truncate_filename_edge_case_exact_length():
    """
    Test that the filename is not truncated if its length is exactly max_length.
    """
    filename = 'exact_length.txt'
    assert truncate_filename(filename, len(filename)) == unquote(filename)


def test_truncate_filename_edge_case_just_over_length():
    """
    Test that the filename is truncated when its length is just over max_length.
    """
    filename = 'a' * 18 + '.txt'  # Length 22
    expected_truncated = unquote('a' * 17) + '...'  # Truncated to 17 + "..." = 20
    assert truncate_filename(filename, 20) == expected_truncated


def test_truncate_filename_empty_string():
    """
    Test with an empty filename.
    """
    filename = ''
    assert truncate_filename(filename, 10) == unquote(filename)


def test_truncate_filename_special_characters():
    """
    Test with a filename containing special characters that need URL encoding.
    """
    filename = 'file with spaces & symbols#.pdf'
    expected_truncated = unquote('file with spaces & sym') + '...'
    assert truncate_filename(filename, 25) == expected_truncated
    assert truncate_filename(filename, 50) == unquote(filename)


def test_truncate_filename_max_length_too_small():
    """
    Test that DocumentValidationError is raised if max_length is less than 4.
    """
    filename = 'test.txt'
    with pytest.raises(DocumentValidationError, match='max_length must be at least 4 to accommodate an ellipsis.'):
        truncate_filename(filename, 3)
    with pytest.raises(DocumentValidationError, match='max_length must be at least 4 to accommodate an ellipsis.'):
        truncate_filename(filename, 0)
