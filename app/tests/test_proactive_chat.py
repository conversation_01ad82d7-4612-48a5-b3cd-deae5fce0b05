from unittest.mock import Mock

from constants.extracted_data import FieldCompletionStatus, ProgressStatus, RequiredField
from constants.message import SystemReplyType
from schemas import AggregatedData, ConversationData
from schemas.confirmed_data import ConfirmedData
from services.proactive_chat import ProactiveChatService


class TestProactiveChatService:
    """Test cases for ProactiveChatService."""

    def setup_method(self):
        """Set up test fixtures."""
        self.conversation_data = ConversationData(
            conversation_message_history=[],
            aggregated_data=AggregatedData(),
            confirmed_data=ConfirmedData(),
            conversation=Mock(),
        )
        self.service = ProactiveChatService(conversation_data=self.conversation_data)

    def test_all_fields_missing(self):
        field_status = self.service._field_status
        for field in RequiredField:
            info = field_status[field]
            assert info.status == FieldCompletionStatus.MISSING
            assert info.value is None
            assert info.display_name

    def test_all_fields_completed(self):
        self.conversation_data.confirmed_data = ConfirmedData(
            client_name='Test Client',
            ldmf_country='United States',
            date_intervals=('2023-01-01', '2023-12-31'),
            objective_and_scope='Test objective',
            outcomes='Test outcomes',
        )
        service = ProactiveChatService(conversation_data=self.conversation_data)
        field_status = service._field_status
        for field in RequiredField:
            info = field_status[field]
            assert info.status == FieldCompletionStatus.COMPLETED
            assert info.value is not None
            assert info.display_name

    def test_pending_confirmation_multiple_ldmf_countries(self):
        self.conversation_data.aggregated_data = AggregatedData(
            client_name=['Test Client'],
            ldmf_country=['Germany', 'Ireland'],
        )
        self.conversation_data.confirmed_data = ConfirmedData(client_name='Test Client')
        service = ProactiveChatService(conversation_data=self.conversation_data)
        field_status = service._field_status
        assert field_status[RequiredField.LDMF_COUNTRY].status == FieldCompletionStatus.PENDING_CONFIRMATION
        assert field_status[RequiredField.LDMF_COUNTRY].value == ['Germany', 'Ireland']
        assert service.next_required_field == RequiredField.LDMF_COUNTRY

    def test_next_required_field(self):
        self.conversation_data.confirmed_data = ConfirmedData(client_name='Test Client')
        service = ProactiveChatService(conversation_data=self.conversation_data)
        # Only client_name is confirmed, so next should be LDMF_COUNTRY
        assert service.next_required_field == RequiredField.LDMF_COUNTRY

    def test_next_required_field_all_completed(self):
        self.conversation_data.confirmed_data = ConfirmedData(
            client_name='Test Client',
            ldmf_country='United States',
            date_intervals=('2023-01-01', '2023-12-31'),
            objective_and_scope='Test objective',
            outcomes='Test outcomes',
        )
        service = ProactiveChatService(conversation_data=self.conversation_data)
        assert service.next_required_field is None

    def test_completion_progress(self):
        self.conversation_data.aggregated_data = AggregatedData(
            client_name=['Test Client'],
            ldmf_country=['United States'],
        )
        self.conversation_data.confirmed_data = ConfirmedData(client_name='Test Client')
        service = ProactiveChatService(conversation_data=self.conversation_data)
        progress = service._completion_progress
        assert progress.total == 5
        assert progress.completed == 1
        assert progress.pending_confirmation == 1
        assert progress.missing == 3
        assert progress.percentage == 20
        assert progress.status == ProgressStatus.IN_PROGRESS

    def test_completion_progress_all_completed(self):
        self.conversation_data.confirmed_data = ConfirmedData(
            client_name='Test Client',
            ldmf_country='United States',
            date_intervals=('2023-01-01', '2023-12-31'),
            objective_and_scope='Test objective',
            outcomes='Test outcomes',
        )
        service = ProactiveChatService(conversation_data=self.conversation_data)
        progress = service._completion_progress
        assert progress.total == 5
        assert progress.completed == 5
        assert progress.pending_confirmation == 0
        assert progress.missing == 0
        assert progress.percentage == 100
        assert progress.status == ProgressStatus.COMPLETED

    def test_enriched_message_missing_fields(self):
        msg, _ = self.service.get_proactive_system_message()
        assert 'We need to collect the Client Name' in msg
        assert 'Please provide the client name for this engagement' in msg

    def test_enriched_message_partial_fields(self):
        self.conversation_data.confirmed_data = ConfirmedData(
            client_name='Test Client',
            ldmf_country='United States',
            date_intervals=('2023-01-01', '2023-12-31'),
        )
        service = ProactiveChatService(conversation_data=self.conversation_data)
        msg, _ = service.get_proactive_system_message()
        assert msg == SystemReplyType.NEED_INFO_OBJECTIVE_SCOPE.message_text

    def test_enriched_message_multiple_ldmf_countries(self):
        self.conversation_data.aggregated_data = AggregatedData(
            client_name=['Test Client'],
            ldmf_country=['Germany', 'Ireland'],
        )
        self.conversation_data.confirmed_data = ConfirmedData(client_name='Test Client')
        service = ProactiveChatService(conversation_data=self.conversation_data)
        msg, _ = service.get_proactive_system_message()
        # Should prompt for LDMF country selection
        assert 'Lead Deloitte Member Firm' in msg
        assert (
            'could you tell me who the lead deloitte member firm is' in msg.lower()
            or 'please specify the Lead Deloitte Member Firm country' in msg
        )
