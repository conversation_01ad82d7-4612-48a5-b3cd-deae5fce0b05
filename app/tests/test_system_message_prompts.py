from unittest.mock import Mock

from constants.extracted_data import ConversationState, FieldCompletionStatus, RequiredField
from constants.message import SystemReplyType
from schemas import AggregatedData, ConfirmedData, ConversationData
from services.system_message_reply_type import SystemMessageReplyTypeGenerator


class TestSystemMessagePromptsGenerator:
    """Test cases for SystemMessagePromptsGenerator service - generates system reply types."""

    def test_generate_system_reply_type_for_empty_data(self):
        """Test system reply type generation when no data is available."""
        aggregated_data = AggregatedData()
        confirmed_data = ConfirmedData()
        conversation = Mock()  # QualConversation
        conversation.State = ConversationState.INITIAL
        conversation_data = ConversationData(
            conversation_message_history=[],
            aggregated_data=aggregated_data,
            confirmed_data=confirmed_data,
            conversation=conversation,
        )
        generator = SystemMessageReplyTypeGenerator(conversation_data=conversation_data)
        reply_type = generator.generate_system_reply_type()

        # Should return None for empty data (no specific reply type needed)
        assert reply_type is None

    def test_generate_system_reply_type_for_client_confirmation(self):
        """Test system reply type generation when client data needs confirmation."""
        aggregated_data = AggregatedData(client_name=['Test Client'])
        confirmed_data = ConfirmedData()
        conversation = Mock()  # QualConversation
        conversation.State = ConversationState.INITIAL
        conversation_data = ConversationData(
            conversation_message_history=[],
            aggregated_data=aggregated_data,
            confirmed_data=confirmed_data,
            conversation=conversation,
        )
        generator = SystemMessageReplyTypeGenerator(conversation_data=conversation_data)
        reply_type = generator.generate_system_reply_type()

        # Should return None for single client (no specific reply type needed)
        assert reply_type is None

    def test_generate_system_reply_type_for_multiple_client_options(self):
        """Test system reply type generation when multiple client options are available."""
        aggregated_data = AggregatedData(client_name=['Client A', 'Client B'])
        confirmed_data = ConfirmedData()
        conversation = Mock()  # QualConversation
        conversation.State = ConversationState.INITIAL
        conversation_data = ConversationData(
            conversation_message_history=[],
            aggregated_data=aggregated_data,
            confirmed_data=confirmed_data,
            conversation=conversation,
        )
        generator = SystemMessageReplyTypeGenerator(conversation_data=conversation_data)
        reply_type = generator.generate_system_reply_type()

        # Should return None for client field (not LDMF country)
        assert reply_type is None

    def test_generate_system_reply_type_for_ldmf_country_missing(self):
        """Test system reply type generation when client is confirmed but LDMF country is missing."""
        aggregated_data = AggregatedData(client_name=['Test Client'])
        confirmed_data = ConfirmedData(client_name='Test Client')
        conversation = Mock()  # QualConversation
        conversation.State = ConversationState.INITIAL
        conversation_data = ConversationData(
            conversation_message_history=[],
            aggregated_data=aggregated_data,
            confirmed_data=confirmed_data,
            conversation=conversation,
        )
        generator = SystemMessageReplyTypeGenerator(conversation_data=conversation_data)
        reply_type = generator.generate_system_reply_type()

        # Should return None for missing LDMF country (no specific reply type needed)
        assert reply_type is None

    def test_generate_system_reply_type_for_engagement_dates_missing(self):
        """Test system reply type generation when client and LDMF are confirmed but dates are missing."""
        aggregated_data = AggregatedData(client_name=['Test Client'], ldmf_country=['United States'])
        confirmed_data = ConfirmedData(client_name='Test Client', ldmf_country='United States')
        conversation = Mock()  # QualConversation
        conversation.State = ConversationState.INITIAL
        conversation_data = ConversationData(
            conversation_message_history=[],
            aggregated_data=aggregated_data,
            confirmed_data=confirmed_data,
            conversation=conversation,
        )
        generator = SystemMessageReplyTypeGenerator(conversation_data=conversation_data)
        reply_type = generator.generate_system_reply_type()

        # Should return None for missing engagement dates (no specific reply type needed)
        assert reply_type is None

    def test_generate_system_reply_type_for_all_fields_complete(self):
        """Test system reply type generation when all required fields are complete."""
        aggregated_data = AggregatedData(
            client_name=['Test Client'],
            ldmf_country=['United States'],
            date_intervals=[('2023-01-01', '2023-12-31')],
            objective_and_scope='Test objective',
            outcomes='Test outcomes',
        )
        confirmed_data = ConfirmedData(
            client_name='Test Client',
            ldmf_country='United States',
            date_intervals=('2023-01-01', '2023-12-31'),
            objective_and_scope='Test objective',
            outcomes='Test outcomes',
        )
        conversation = Mock()  # QualConversation
        conversation.State = ConversationState.INITIAL
        conversation_data = ConversationData(
            conversation_message_history=[],
            aggregated_data=aggregated_data,
            confirmed_data=confirmed_data,
            conversation=conversation,
        )
        generator = SystemMessageReplyTypeGenerator(conversation_data=conversation_data)
        reply_type = generator.generate_system_reply_type()

        # Should return None when all fields are complete (no specific reply type needed)
        assert reply_type == SystemReplyType.CONFIRMED_FIELDS_READY

    def test_generate_system_reply_type_for_multiple_ldmf_countries(self):
        """Test system reply type generation when multiple LDMF countries are available."""
        from constants.message import SystemReplyType

        aggregated_data = AggregatedData(
            client_name=['Test Client'], ldmf_country=['United States', 'United Kingdom', 'Canada']
        )
        confirmed_data = ConfirmedData(client_name='Test Client')
        conversation = Mock()  # QualConversation
        conversation.State = ConversationState.INITIAL
        conversation_data = ConversationData(
            conversation_message_history=[],
            aggregated_data=aggregated_data,
            confirmed_data=confirmed_data,
            conversation=conversation,
        )
        generator = SystemMessageReplyTypeGenerator(conversation_data=conversation_data)
        reply_type = generator.generate_system_reply_type()

        # Should return LDMF_COUNTRY_MULTIPLE_OPTIONS for multiple LDMF countries
        assert reply_type == SystemReplyType.LDMF_COUNTRY_MULTIPLE_OPTIONS

    def test_field_status_detection(self):
        """Test internal field status detection logic."""
        aggregated_data = AggregatedData(
            client_name=['Test Client'],
            ldmf_country=['US', 'UK'],  # Multiple options
        )
        confirmed_data = ConfirmedData(client_name='Test Client')
        conversation = Mock()  # QualConversation
        conversation.State = ConversationState.INITIAL
        conversation_data = ConversationData(
            conversation_message_history=[],
            aggregated_data=aggregated_data,
            confirmed_data=confirmed_data,
            conversation=conversation,
        )
        generator = SystemMessageReplyTypeGenerator(conversation_data=conversation_data)
        field_status = generator._get_field_status()

        # Client should be completed
        assert field_status[RequiredField.CLIENT_NAME].status == FieldCompletionStatus.COMPLETED

        # LDMF should be pending confirmation
        assert field_status[RequiredField.LDMF_COUNTRY].status == FieldCompletionStatus.PENDING_CONFIRMATION

        # Dates should be missing
        assert field_status[RequiredField.ENGAGEMENT_DATES].status == FieldCompletionStatus.MISSING

    def test_next_required_field_detection(self):
        """Test detection of the next required field."""
        aggregated_data = AggregatedData(client_name=['Test Client'])
        confirmed_data = ConfirmedData(client_name='Test Client')
        conversation = Mock()  # QualConversation
        conversation.State = ConversationState.INITIAL
        conversation_data = ConversationData(
            conversation_message_history=[],
            aggregated_data=aggregated_data,
            confirmed_data=confirmed_data,
            conversation=conversation,
        )
        generator = SystemMessageReplyTypeGenerator(conversation_data=conversation_data)
        field_status = generator._get_field_status()
        next_field = generator._get_next_required_field(field_status)

        # Should be LDMF country since client is complete
        assert next_field == RequiredField.LDMF_COUNTRY
