from fastapi import status

from config import settings
from constants.operation_ids import operation_ids
from core.http_client import CustomAsyncClient


async def test_health_check(async_client: CustomAsyncClient, url_resolver):
    url = url_resolver.reverse(operation_ids.root.HEALTH_CHECK)

    response = await async_client.get(url)

    expected = {
        'service': settings.project_name,
        'healthy': True,
        'api_version': settings.version,
        'error': None,
    }

    assert response.status_code == status.HTTP_200_OK
    assert response.json() == expected
    assert response.headers['content-type'] == 'application/json'
