import sqlalchemy as sa
from sqlalchemy.orm import relationship

from constants.extracted_data import DataSourceType
from core.db import Base


__all__ = ['QualExtractedData']


class QualExtractedData(Base):
    __tablename__ = 'QualExtractedData'

    Id = sa.Column(sa.Integer, primary_key=True, autoincrement=True)
    QualConversationId = sa.Column(sa.Integer, sa.ForeignKey('QualConversation.Id'), nullable=False)
    DataSourceType = sa.Column(sa.Enum(DataSourceType, values_callable=lambda x: [e.value for e in x]), nullable=False)
    CreatedAt = sa.Column(sa.DateTime, server_default=sa.func.now(), nullable=False)

    # Existing core fields
    ActivityId = sa.Column(sa.Integer, nullable=True, index=True)
    ActivityName = sa.Column(sa.UnicodeText, nullable=True)
    ClientName = sa.Column(sa.UnicodeText, nullable=True)
    LDMFCountry = sa.Column(sa.UnicodeText, nullable=True)
    Title = sa.Column(sa.UnicodeText, nullable=True)
    StartDate = sa.Column(sa.Date, nullable=True)
    EndDate = sa.Column(sa.Date, nullable=True)
    StartDateOriginal = sa.Column(sa.UnicodeText, nullable=True)
    EndDateOriginal = sa.Column(sa.UnicodeText, nullable=True)
    MultipleDates = sa.Column(sa.Boolean, nullable=True)
    Industries = sa.Column(sa.UnicodeText, nullable=True)
    Services = sa.Column(sa.UnicodeText, nullable=True)
    Roles = sa.Column(sa.UnicodeText, nullable=True)
    ObjectiveAndScope = sa.Column(sa.UnicodeText, nullable=True)
    Outcomes = sa.Column(sa.UnicodeText, nullable=True)

    # Engagement Description fields
    BusinessIssues = sa.Column(sa.UnicodeText, nullable=True)
    ScopeApproach = sa.Column(sa.UnicodeText, nullable=True)
    ValueDelivered = sa.Column(sa.UnicodeText, nullable=True)
    EngagementSummary = sa.Column(sa.UnicodeText, nullable=True)
    OneLineDescription = sa.Column(sa.UnicodeText, nullable=True)

    # Engagement Details fields
    ClientReferences = sa.Column(sa.UnicodeText, nullable=True)
    ClientNameSharing = sa.Column(sa.UnicodeText, nullable=True)
    ClientIndustry = sa.Column(sa.UnicodeText, nullable=True)
    EngagementDates = sa.Column(sa.UnicodeText, nullable=True)
    EngagementLocations = sa.Column(sa.UnicodeText, nullable=True)
    EngagementFeeDisplay = sa.Column(sa.UnicodeText, nullable=True)
    ClientServices = sa.Column(sa.UnicodeText, nullable=True)
    SourceOfWork = sa.Column(sa.UnicodeText, nullable=True)

    # Usage & Team fields
    QualUsage = sa.Column(sa.UnicodeText, nullable=True)
    TeamRoles = sa.Column(sa.UnicodeText, nullable=True)
    Approver = sa.Column(sa.UnicodeText, nullable=True)

    Conversation = relationship('QualConversation', back_populates='ExtractedData')

    __table_args__ = (sa.UniqueConstraint('QualConversationId', 'DataSourceType', name='uq_conversation_source_type'),)
