import sqlalchemy as sa
from sqlalchemy.orm import relationship

from core.db import Base


__all__ = ['QualDocument']


class QualDocument(Base):
    __tablename__ = 'QualDocument'

    Id = sa.Column(sa.Integer, primary_key=True, autoincrement=True)
    PublicId = sa.Column(sa.Uuid, unique=True, nullable=False, server_default=sa.text('NEWID()'))
    QualConversationMessageId = sa.Column(sa.Integer, sa.ForeignKey('QualConversationMessage.Id'), nullable=False)
    FileName = sa.Column(sa.UnicodeText, nullable=False)
    FileSize = sa.Column(sa.Integer, nullable=False)
    FileType = sa.Column(sa.String, nullable=False)
    FileUrl = sa.Column(sa.UnicodeText, nullable=False)

    Message = relationship('QualConversationMessage', back_populates='Documents')
