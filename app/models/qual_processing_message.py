from json import JSONDecodeError
from typing import Any

import sqlalchemy as sa
from sqlalchemy.orm import relationship
import sqlalchemy.types as types

from core import json
from core.db import Base


__all__ = ['QualProcessingMessage']


class JSONEncodedDict(types.TypeDecorator):
    """
    Enables JSON storage by encoding and decoding on the fly.
    Stores as TEXT in the database.
    """

    impl = types.UnicodeText

    cache_ok = True

    def process_bind_param(self, value: dict[str, Any] | None, dialect):
        """
        Convert Python dictionary to JSON string when writing to DB.
        """
        if value is not None:
            return json.dumps(value)
        return None

    def process_result_value(self, value: str | None, dialect):
        """
        Convert JSON string to Python dictionary when reading from DB.
        """
        if value is not None:
            try:
                return json.loads(value)
            except (JSONDecodeError, TypeError):
                # Handle cases where the stored string might not be valid JSON
                # or is already None/empty string.
                # You might want to log this error or return an empty dict/None based on your needs.
                return None  # Or {}
        return None


class QualProcessingMessage(Base):
    """
    SQLAlchemy model for storing processing message statuses.
    """

    __tablename__ = 'QualProcessingMessage'

    Id = sa.Column(sa.Integer, primary_key=True, autoincrement=True)
    Status = sa.Column(sa.String, nullable=False)
    Message = sa.Column(sa.String, nullable=True)
    Metadata = sa.Column(JSONEncodedDict, nullable=True)
    QualConversationMessageId = sa.Column(sa.Integer, sa.ForeignKey('QualConversationMessage.Id'), nullable=False)

    ConversationMessage = relationship('QualConversationMessage', back_populates='ProcessingMessages')
