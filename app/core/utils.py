from pathlib import Path
from urllib.parse import unquote

from exceptions.document import DocumentValidationError


__all__ = ['load_file_from_folder', 'truncate_filename']


def truncate_filename(filename: str, max_length: int = 20) -> str:
    """
    Truncates a filename to a specified maximum length, adding an ellipsis if truncated.

    Args:
        filename: The name of the file.
        max_length: The maximum length of the truncated filename.

    Returns:
        The truncated filename.
    """
    if max_length < 4:
        raise DocumentValidationError('max_length must be at least 4 to accommodate an ellipsis.')
    if len(filename) > max_length:
        return f'{unquote(filename[: max_length - 3])}...'
    return unquote(filename)


def load_file_from_folder(folder: str, file_name: str) -> str:
    path = Path(__file__).parent.parent / 'assets' / folder / file_name
    return path.read_text(encoding='utf-8').strip()
