import enum
from typing import ValuesView


__all__ = ['StrEnum', 'IntEnum']


class CustomEnum(enum.Enum):
    @classmethod
    def all(cls) -> ValuesView:
        return cls.__members__.values()

    @classmethod
    def values(cls) -> tuple:
        return tuple(x.value for x in cls.all())

    def __hash__(self):
        return hash(repr(self))


class StrEnum(CustomEnum, enum.StrEnum):
    def __str__(self) -> str:
        return self._value_


class IntEnum(CustomEnum, enum.IntEnum):
    def __int__(self) -> int:
        return self._value_
