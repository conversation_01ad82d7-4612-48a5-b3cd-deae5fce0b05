from typing import Any, Dict
from urllib.parse import urljoin

from fastapi import FastAP<PERSON>
from fastapi.routing import APIRoute


__all__ = ['url_join', 'URLResolver']


def url_join(base: str, path: str) -> str:
    return urljoin(base + ('' if base.endswith('/') else '/'), path)


class URLResolver:
    def __init__(self, app: FastAPI):
        self.app = app
        self._routes_map = self._build_routes_map()

    def _build_routes_map(self) -> Dict[str, str]:
        routes_map = {}
        for route in self.app.routes:
            if isinstance(route, APIRoute) and route.operation_id:
                routes_map[route.operation_id] = route.path
        return routes_map

    def reverse(self, operation_id: str, **kwargs: Any) -> str:
        """
        Get URL path by operation_id with optional parameters.

        Usage:
            url = url_resolver.reverse('get_conversation', conversation_id='123')
        """
        if operation_id not in self._routes_map:
            raise KeyError(f'No route found for operation_id: {operation_id}')

        path = self._routes_map[operation_id]
        path = path.format(**kwargs)
        return path
