import datetime as dt
import os

from dotenv import find_dotenv, load_dotenv
from pydantic import BaseModel, ConfigDict

from constants.environment import Environment


__all__ = ['CustomModel']

if env_file := os.getenv('ENV_FILE'):
    load_dotenv(dotenv_path=find_dotenv(filename=env_file), override=True)

# NOTE: This has been made for purpose as I wouldn't like to import settings to schemas
ENVIRONMENT = Environment(os.environ['ENVIRONMENT'])


class CustomModel(BaseModel):
    model_config = ConfigDict(
        hide_input_in_errors=ENVIRONMENT not in (Environment.LOCAL, Environment.DEV, Environment.QA),
        validate_by_name=True,
        validate_by_alias=True,
    )

    def serialize_date(self, value: dt.date | None, _info) -> str | None:
        return value.isoformat() if value else None
