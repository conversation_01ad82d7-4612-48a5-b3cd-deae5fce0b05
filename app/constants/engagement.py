from dataclasses import dataclass

from core.utils import load_file_from_folder


__all__ = [
    'FIRST_WELCOME_MESSAGE',
    'engagement_templates',
]

FIRST_WELCOME_MESSAGE = load_file_from_folder('engagement_details', 'welcome_message_assistant.txt')


@dataclass(frozen=True)
class WelcomeMessageTemplates:
    SYSTEM: str = FIRST_WELCOME_MESSAGE


@dataclass(frozen=True)
class EngagementTemplates:
    welcome: WelcomeMessageTemplates = WelcomeMessageTemplates()


engagement_templates = EngagementTemplates()
