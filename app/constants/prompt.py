from dataclasses import dataclass

from core.utils import load_file_from_folder


__all__ = [
    'EXTRACT_DATA_SYSTEM_PROMPT',
    'EXTRACT_DATA_USER_PROMPT',
    'EXTRACT_DATES_SYSTEM_PROMPT',
    'EXTRACT_DATES_USER_PROMPT',
    'EXTRACT_DATES_DATE_IS_TEXT_SYSTEM_PROMPT',
    'EXTRACT_DATES_DATE_IS_TEXT_USER_PROMPT',
    'EXTRACT_LDMF_COUNTRY_SYSTEM_PROMPT',
    'EXTRACT_LDMF_COUNTRY_USER_PROMPT',
    'prompt_templates',
]

EXTRACT_DATA_SYSTEM_PROMPT = load_file_from_folder('extract_data_prompts', 'extract_data_system_prompt.txt')
EXTRACT_DATA_USER_PROMPT = load_file_from_folder('extract_data_prompts', 'extract_data_user_prompt.txt')
EXTRACT_DATES_SYSTEM_PROMPT = load_file_from_folder('extract_data_prompts', 'extract_dates_system_prompt.txt')
EXTRACT_DATES_USER_PROMPT = load_file_from_folder('extract_data_prompts', 'extract_dates_user_prompt.txt')
EXTRACT_DATES_DATE_IS_TEXT_SYSTEM_PROMPT = load_file_from_folder(
    'extract_data_prompts', 'extract_dates_date_is_text_system_prompt.txt'
)
EXTRACT_DATES_DATE_IS_TEXT_USER_PROMPT = load_file_from_folder(
    'extract_data_prompts', 'extract_dates_date_is_text_user_prompt.txt'
)
EXTRACT_LDMF_COUNTRY_SYSTEM_PROMPT = load_file_from_folder(
    'ldmf_country_prompts', 'extract_and_validate_countries_data_system_prompt.txt'
)
EXTRACT_LDMF_COUNTRY_USER_PROMPT = load_file_from_folder(
    'ldmf_country_prompts', 'extract_and_validate_countries_user_prompt.txt'
)
SUMMARIZE_OTHER_FIELD_SYSTEM_PROMPT = load_file_from_folder('summarize_other_field_prompts', 'system_prompt.txt')
SUMMARIZE_OTHER_FIELD_USER_PROMPT = load_file_from_folder('summarize_other_field_prompts', 'user_prompt.txt')


@dataclass(frozen=True)
class ExtractDataPrompts:
    SYSTEM: str = EXTRACT_DATA_SYSTEM_PROMPT
    USER: str = EXTRACT_DATA_USER_PROMPT


@dataclass(frozen=True)
class ExtractDatesPrompts:
    SYSTEM: str = EXTRACT_DATES_SYSTEM_PROMPT
    USER: str = EXTRACT_DATES_USER_PROMPT
    DATE_IS_TEXT_SYSTEM: str = EXTRACT_DATES_DATE_IS_TEXT_SYSTEM_PROMPT
    DATE_IS_TEXT_USER: str = EXTRACT_DATES_DATE_IS_TEXT_USER_PROMPT


@dataclass(frozen=True)
class ExtractLDMFCountryPrompts:
    SYSTEM: str = EXTRACT_LDMF_COUNTRY_SYSTEM_PROMPT
    USER: str = EXTRACT_LDMF_COUNTRY_USER_PROMPT


@dataclass(frozen=True)
class SummarizeOtherFieldPrompts:
    SYSTEM: str = SUMMARIZE_OTHER_FIELD_SYSTEM_PROMPT
    USER: str = SUMMARIZE_OTHER_FIELD_USER_PROMPT


@dataclass(frozen=True)
class PromptTempaltes:
    extract_data: ExtractDataPrompts = ExtractDataPrompts()
    extract_dates: ExtractDatesPrompts = ExtractDatesPrompts()
    extract_ldmf_country: ExtractLDMFCountryPrompts = ExtractLDMFCountryPrompts()
    summarize_other_field: SummarizeOtherFieldPrompts = SummarizeOtherFieldPrompts()


prompt_templates = PromptTempaltes()
