import base64
import logging
import re
from typing import Callable, Mapping
from uuid import UUID

from fastapi import Request, Response
from fastapi.routing import APIRoute
from fastapi.security.utils import get_authorization_scheme_param
from jose import jwt
from jose.exceptions import ExpiredSignatureError, J<PERSON><PERSON>laimsError, JWTError
import rsa
from starlette.middleware.base import BaseHTTPMiddleware

from config import settings
from core.http_client import CustomAsyncClient
from exceptions import InvalidAuthentication, InvalidAuthorization
from schemas import User


__all__ = ['AzureADAuthorizerMiddleware']


logger = logging.getLogger(__name__)


class AzureADAuthorizerMiddleware(BaseHTTPMiddleware):
    VALIDATION_OPTIONS = {
        'require_aud': True,
        'require_exp': True,
        'require_iss': True,
        'require_iat': True,
        'require_nbf': True,
        'require_sub': True,
        'verify_aud': True,
        'verify_exp': True,
        'verify_iat': True,
        'verify_iss': True,
        'verify_nbf': True,
        'verify_sub': True,
    }
    JWT_KEYS_CACHE: dict = {}  # Azure AD JWT keys cache
    AUTH_FREE_ENDPOINT_PATHS: tuple[tuple[set[str], re.Pattern], ...] = ()

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        if self._auth_required(request):
            authorization = request.headers.get('Authorization')
            if not authorization:
                raise InvalidAuthentication('Authorization header is missing or empty')

            scheme, token = get_authorization_scheme_param(authorization)
            if scheme.lower() != 'bearer':
                raise InvalidAuthentication('Wrong authorization scheme')

            if not token:
                raise InvalidAuthentication('Token is missing')

            self._validate_token_scopes(token)
            decoded_token = await self._decode_token(token)
            self._validate_user_email(decoded_token)

            request.state.user = await self._get_user(decoded_token)

        return await call_next(request)

    @classmethod
    def _auth_required(cls, request: Request) -> bool:
        cls._set_auth_free_endpoint_paths(request)
        request_path = request.url.path
        if request_path.startswith(request.app.root_path + '/'):
            request_path = request.url.path[len(request.app.root_path) :]
        for methods, path_regex in cls.AUTH_FREE_ENDPOINT_PATHS:
            if request.method in methods and path_regex.match(request_path):
                return False

        return True

    @classmethod
    def _set_auth_free_endpoint_paths(cls, request: Request):
        if not cls.AUTH_FREE_ENDPOINT_PATHS:
            result = [
                (route.methods, route.path_regex)
                for route in request.app.routes
                if not isinstance(route, APIRoute) or route.operation_id in settings.auth.auth_free_endpoints
            ]
            result.append(({'GET'}, re.compile(r'^/favicon.ico$')))
            cls.AUTH_FREE_ENDPOINT_PATHS = tuple(result)

    @staticmethod
    def _validate_token_scopes(token: str):
        """
        Validate that the requested scopes are in the tokens claims
        """
        try:
            claims = jwt.get_unverified_claims(token) or {}
        except Exception:
            msg = 'Malformed token'
            logger.debug(f'{msg}: %s', token)
            raise InvalidAuthentication(msg)

        try:
            token_scopes = claims.get('scp', '').split(' ')
        except Exception:
            msg = 'Malformed scopes'
            logger.debug(msg)
            raise InvalidAuthentication(msg)

        if not (settings.auth.ad.required_scopes <= frozenset(token_scopes)):
            msg = 'Missing a required scope'
            required = tuple(sorted(settings.auth.ad.required_scopes))
            scp = tuple(sorted(token_scopes))
            logger.debug(f'{msg}. Required: {required}. In token: {scp}')
            raise InvalidAuthentication(msg)

    async def _decode_token(self, token: str) -> Mapping:
        headers = jwt.get_unverified_header(token)
        key_id = headers.get('kid') if headers else None
        if not key_id:
            raise InvalidAuthentication('The token does not contain "kid"')

        key = await self._get_token_key(key_id)
        try:
            return jwt.decode(
                token=token,
                key=key,
                algorithms=('RS256',),
                audience=settings.auth.ad.api_audience,
                options=self.VALIDATION_OPTIONS,
            )

        except Exception as e:
            match e:
                case JWTClaimsError():
                    msg = 'The token has some invalid claims'
                case ExpiredSignatureError():
                    msg = 'The token signature has expired'
                case JWTError():
                    msg = 'The token is invalid'
                case _:
                    msg = 'Unexpected token decode error'
            logger.debug(f'{msg}: %s', e)
            raise InvalidAuthentication(msg)

    async def _get_token_key(self, key_id: str) -> str:
        if key_id not in self.JWT_KEYS_CACHE:
            type(self).JWT_KEYS_CACHE = await self._get_jwt_keys()
        if key_id not in self.JWT_KEYS_CACHE:
            logger.debug('Key not found in the keys cache: %s', key_id)
            raise InvalidAuthentication('Unable to find matching key for token validation')

        return self.JWT_KEYS_CACHE[key_id]

    @classmethod
    async def _get_jwt_keys(cls):
        result = {}
        async with CustomAsyncClient(settings=settings) as http_client:
            oid_conf_response = await http_client.get(
                f'{settings.auth.ad.aad_instance}/{settings.auth.ad.aad_tenant_id}'
                '/v2.0/.well-known/openid-configuration'
            )
            aad_metadata = oid_conf_response.json() if oid_conf_response.is_success else None
            if jwks_uri := aad_metadata.get('jwks_uri') if aad_metadata else None:
                keys_response = await http_client.get(jwks_uri)
                keys = keys_response.json() if keys_response.is_success else None
                for key in (keys or {}).get('keys', ()):
                    pub_key = rsa.PublicKey(
                        int.from_bytes(base64.urlsafe_b64decode(cls._ensure_b64padding(key['n'])), 'big'),
                        int.from_bytes(base64.urlsafe_b64decode(cls._ensure_b64padding(key['e'])), 'big'),
                    )
                    result[key['kid']] = pub_key.save_pkcs1()
        return result

    @staticmethod
    def _ensure_b64padding(key: str) -> str:
        """
        The base64 encoded keys are not always correctly padded, so pad with the right number of =
        """
        return key + '=' * (len(key) % 4)

    async def _get_user(self, decoded_token: Mapping) -> User:
        try:
            user_id = UUID(decoded_token['oid'])
        except Exception as e:
            logger.debug(e)
            raise InvalidAuthentication('Unable to extract user details from token')

        return User.model_validate(
            {
                'id': user_id,
                'full_name': decoded_token.get('name'),
                'email': decoded_token.get('unique_name'),
                'first_name': decoded_token.get('given_name'),
                'last_name': decoded_token.get('family_name'),
                'roles': decoded_token.get('roles', ()),
            }
        )

    @staticmethod
    def _validate_user_email(decoded_token: Mapping):
        user_email = (decoded_token.get('email') or '').strip()
        if not user_email:
            raise InvalidAuthentication('Token lacks the "email" claim')

        if any(map(lambda x: user_email.endswith(x), settings.auth.ad.blocked_domains)):
            raise InvalidAuthorization('The domain is blocked')
