from dataclasses import dataclass
import json
import logging
from typing import Type, cast

from openai import BadR<PERSON><PERSON><PERSON>rror
from openai.types.chat import (
    ChatCompletionMessageParam,
    ChatCompletionSystemMessageParam,
    ChatCompletionUserMessageParam,
)

from config import settings
from constants.message import GENERAL_INTENTIONS, INTENT_OBJECT_STRUCTURE, ConversationMessageIntention
from core.schemas import CustomModel
from exceptions import AIBadRequestError
from repositories import OpenAIRepository
from schemas.conversation_message import ConversationMessageIntentClassifierServiceResponse


__all__ = ['IntentClassifierService']


logger = logging.getLogger(__name__)


SYSTEM_PROMPT = """
CONTEXT: You work on a project that automates Qual Generation.
Qual is short for "quantitive analysis". It is a report that is generated based on the project description, provided by a user.

INPUT: You will be provided with a list of intention categories in valid JSON format.
Each intention category object within this list will conform to the following structure:
{intent_object_structure}.

After that you will receive user's message to analyse, provided in triple quotes.

ROLE: You are an Intention Classifier. A user types their request, and you must understand the intention of the user by classifying it into one of the predefined Intenion Categories.
Your role is similar to being a receptionist. Imagine that the user walks in and says their request to you.

OBJECTIVE: Your task is to identify what the user requests at the moment.
The following JSON array contains all the intention classes you must use.
Please refer to this list for classification:
{intents_descriptions}.


OUTPUT: Then you pass this category to your manager saying only the name of this category.
"""

USER_PROMPT = """
Here is the user's message:
{user_message}

Think step by step: first analyse the message, make a short summary to understand what the request is about.
Then using the provided JSON file with all the intentions, find the best fit out of those categories.
"""


class IntentInfo(CustomModel):
    """Information about an intent."""

    name: str
    description: str
    user_message_examples: list[str]


@dataclass(frozen=True)
class IntentClassifierService:
    """Classifies user messages into intents."""

    openai_service: OpenAIRepository
    temperature: float = settings.openai.default_temperature

    def _get_user_prompt(
        self,
        *,
        user_message: str,
    ) -> str:
        return USER_PROMPT.format(
            user_message=user_message,
        )

    def _get_system_prompt(
        self,
        *,
        intent_object_structure: str,
        intents_descriptions: str,
    ) -> str:
        return SYSTEM_PROMPT.format(
            intent_object_structure=intent_object_structure,
            intents_descriptions=intents_descriptions,
        )

    @classmethod
    def _get_formatted_intents_info(cls) -> str:
        """
        Build a prompt string containing descriptions of all intents in this enum.

        Returns:
        A json-string with all intent descriptions.
        """

        intents_info = cls.get_intents_info()
        serializable_intents_info = {intent.value: info.model_dump() for intent, info in intents_info.items()}
        return json.dumps(serializable_intents_info, indent=2)

    @staticmethod
    def get_intents_info() -> dict[ConversationMessageIntention, IntentInfo]:
        intentions = GENERAL_INTENTIONS['Intentions']
        info = {}
        for intention in intentions:
            name = intention['intentionName']
            try:
                info[ConversationMessageIntention(name)] = IntentInfo(
                    name=name,
                    description=intention['description'],
                    user_message_examples=intention['userMessageExamples'],
                )
            except ValueError:
                logger.exception(
                    'Couldnt parse intention name %s from file to ConversationMessageIntention',
                    name,
                )
                raise
        return info

    @staticmethod
    def _create_system_message(message: str) -> ChatCompletionSystemMessageParam:
        """Create a system message for the chat completion."""
        return {'role': 'system', 'content': message}

    @staticmethod
    def _create_user_message(message: str) -> ChatCompletionUserMessageParam:
        """Create a user message for the chat completion."""
        return {'role': 'user', 'content': message}

    async def classify_intent(
        self,
        *,
        user_message: str,
        response_cls: Type[ConversationMessageIntentClassifierServiceResponse],
    ) -> ConversationMessageIntentClassifierServiceResponse:
        """
        Classify the user's message intent using an LLM.

        Args:
            user_message: The user's message content.

        Returns:
            The classified intent.
        """

        # Prepare the messages
        system_message = self._create_system_message(
            self._get_system_prompt(
                intent_object_structure=INTENT_OBJECT_STRUCTURE,
                intents_descriptions=self._get_formatted_intents_info(),
            )
        )
        user_message_param = self._create_user_message(
            self._get_user_prompt(
                user_message=user_message,
            )
        )

        # Call the OpenAI API
        try:
            response = await self.openai_service.generate_chat_completion(
                messages=[
                    cast(ChatCompletionMessageParam, system_message),
                    cast(ChatCompletionMessageParam, user_message_param),
                ],
                temperature=self.temperature,
                response_format=response_cls,
            )
        except BadRequestError:
            raise AIBadRequestError()

        if not isinstance(response, ConversationMessageIntentClassifierServiceResponse):
            raise RuntimeError(f'Invalid response from model, expected {response_cls}, got {type(response)}')

        return response
