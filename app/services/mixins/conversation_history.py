from dataclasses import dataclass
from functools import cached_property

from constants.message import SystemReplyType
from schemas.conversation_message.message import CombinedMessageSerializer


__all__ = [
    'CombinedHistoryMixin',
]


@dataclass(frozen=True)
class CombinedHistoryMixin:
    conversation_message_history: list[CombinedMessageSerializer]

    @cached_property
    def _latest_combined_message(self) -> CombinedMessageSerializer | None:
        return self.conversation_message_history[-1] if self.conversation_message_history else None

    @cached_property
    def _latest_system_message(self) -> str | None:
        if self._latest_combined_message:
            assert self._latest_combined_message.system, (
                'Conversation message history is corrupted, found empty system message'
            )
            return self._latest_combined_message.system.content.strip()
        return None

    @cached_property
    def _latest_system_message_type(self) -> SystemReplyType | None:
        return (
            self._latest_combined_message.system.system_reply_type
            if self._latest_combined_message and self._latest_combined_message.system
            else None
        )

    @cached_property
    def _latest_user_message(self) -> str | None:
        return self._latest_combined_message.user.content.strip() if self._latest_combined_message else None
