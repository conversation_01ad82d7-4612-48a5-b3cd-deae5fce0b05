from datetime import datetime, timedelta, timezone
import logging
import re
from urllib.parse import urljoin
from uuid import UUID

import jwt

from config import settings
from constants.auth import JwtClaim
from core.json import CustomJSONEncoder
from schemas import SignalRToken


__all__ = ['AuthService']


logger = logging.getLogger(__name__)


class AuthService:
    """Auth service."""

    _CONN_STRING_PARSER_RE = re.compile(r'(\w+)=([^;]+)')

    def __init__(self):
        signal_r_parsed = dict(self._CONN_STRING_PARSER_RE.findall(settings.auth.signal_r.connection_string))
        if 'AccessKey' not in signal_r_parsed or 'Endpoint' not in signal_r_parsed:
            raise ValueError('SignalR connection string is missing required keys: "AccessKey" or/and "Endpoint"')

        self.signal_r_access_key = signal_r_parsed['AccessKey']
        self.signal_r_endpoint = signal_r_parsed['Endpoint']

    def generate_signal_r_jwt(self, conversation_id: UUID) -> SignalRToken:
        """
        Create a new SignalR JWT.
        """
        claims = {
            str(JwtClaim.EXP): datetime.now(timezone.utc) + timedelta(minutes=settings.auth.signal_r.token_lifespan),
            str(JwtClaim.AUD): urljoin(self.signal_r_endpoint, f'client/?hub={settings.auth.signal_r.hub_name}'),
            str(JwtClaim.SUB): str(conversation_id),
        }
        token = jwt.encode(claims, self.signal_r_access_key, algorithm='HS256', json_encoder=CustomJSONEncoder)
        return SignalRToken(token=token)
