import logging
from typing import Any

from repositories.service import ServiceRepository


__all__ = ['ServiceDataService']

logger = logging.getLogger(__name__)


class ServiceDataService:
    cached_services: dict[str, dict[str, Any]] = {}

    def __init__(self, service_repository: ServiceRepository):
        self.service_repository = service_repository

    @classmethod
    async def _set_cached_services(cls, services: list[dict[str, Any]]) -> None:
        cls.cached_services = {service['name']: service for service in services}

    async def list(self, token: str) -> dict[str, dict[str, Any]]:
        """
        List all services.

        Returns:
            List of services.

        Raises:
            Exception: If an error occurs while listing services.
        """
        try:
            logger.info('Listing services')
            if not self.cached_services:
                services = await self.service_repository.list(token)
                await self._set_cached_services(services)

            return self.cached_services
        except Exception as e:
            logger.error('Error listing services: %s', e)
            raise
