"""Enhanced date interval strategy for additional date fields."""

from schemas import ExtractedData

from .base import BaseAggregationStrategy


class EnhancedDateIntervalCollectionStrategy(BaseAggregationStrategy):
    """Strategy for collecting date intervals for additional date fields."""

    def __init__(self):
        # Existing fields
        self._date_intervals: list[tuple[str | None, str | None]] = []
        self._date_intervals_original: list[tuple[str | None, str | None]] = []

        # New fields that need date interval collection
        self._engagement_dates: list[str] = []

    def process(self, extracted_data: ExtractedData) -> None:
        """Collect date intervals from extracted data."""
        # Existing fields
        if extracted_data.start_date and extracted_data.end_date:
            self._date_intervals.append(
                (
                    extracted_data.start_date.isoformat() if extracted_data.start_date else None,
                    extracted_data.end_date.isoformat() if extracted_data.end_date else None,
                )
            )

        if extracted_data.start_date_original and extracted_data.end_date_original:
            self._date_intervals_original.append((extracted_data.start_date_original, extracted_data.end_date_original))

        # New fields
        if extracted_data.engagement_dates:
            self._add_engagement_dates(extracted_data.engagement_dates)

    def _add_engagement_dates(self, dates: str) -> None:
        """Add engagement dates, handling various formats."""
        dates = dates.strip()
        if dates and dates not in self._engagement_dates:
            self._engagement_dates.append(dates)

    def get_date_intervals(self) -> list[tuple[str | None, str | None]]:
        """Get collected date intervals."""
        return self._date_intervals

    def get_date_intervals_original(self) -> list[tuple[str | None, str | None]]:
        """Get collected original date intervals."""
        return self._date_intervals_original

    def get_engagement_dates(self) -> list[str]:
        """Get collected engagement dates."""
        return self._engagement_dates
