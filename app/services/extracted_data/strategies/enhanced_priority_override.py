"""Enhanced priority override strategy for additional fields."""

from schemas import ExtractedData

from .base import BaseAggregationStrategy


class EnhancedPriorityOverrideStrategy(BaseAggregationStrategy):
    """Strategy for priority override behavior where latest value wins for additional fields."""

    def __init__(self):
        # Existing fields
        self._objective_and_scope: str | None = None
        self._outcomes: str | None = None

        # New fields that need override behavior
        self._engagement_fee_display: str | None = None

    def process(self, extracted_data: ExtractedData) -> None:
        """Apply priority override for specified fields (latest value wins)."""
        # Existing fields
        if extracted_data.objective_and_scope:
            self._objective_and_scope = extracted_data.objective_and_scope
        if extracted_data.outcomes:
            self._outcomes = extracted_data.outcomes

        # New fields
        if extracted_data.engagement_fee_display:
            self._engagement_fee_display = extracted_data.engagement_fee_display

    def get_objective_and_scope(self) -> str | None:
        """Get objective and scope (latest value)."""
        return self._objective_and_scope

    def get_outcomes(self) -> str | None:
        """Get outcomes (latest value)."""
        return self._outcomes

    def get_engagement_fee_display(self) -> str | None:
        """Get engagement fee display (latest value)."""
        return self._engagement_fee_display
