from datetime import date

from schemas import ExtractedData

from .base import BaseAggregationStrategy


__all__ = ['DateIntervalCollectionStrategy']


class DateIntervalCollectionStrategy(BaseAggregationStrategy):
    """Strategy for collecting date intervals as pairs from all sources."""

    def __init__(self):
        self._all_start_dates: list[date] = []
        self._all_end_dates: list[date] = []
        self._all_original_start_dates: list[str] = []
        self._all_original_end_dates: list[str] = []
        self._more_than_two_dates: bool = False

    def process(self, extracted_data: ExtractedData) -> None:
        """Collect date intervals from extracted data."""
        if extracted_data.start_date:
            self._all_start_dates.append(extracted_data.start_date)
        if extracted_data.end_date:
            self._all_end_dates.append(extracted_data.end_date)
        if extracted_data.start_date_original:
            self._all_original_start_dates.append(extracted_data.start_date_original)
        if extracted_data.end_date_original:
            self._all_original_end_dates.append(extracted_data.end_date_original)
        if extracted_data.more_than_two_dates:
            self._more_than_two_dates = True

    def get_date_intervals(self) -> list[tuple[str | None, str | None]]:
        """Get list of parsed date intervals."""
        final_start_date = min(self._all_start_dates) if self._all_start_dates else None
        final_end_date = max(self._all_end_dates) if self._all_end_dates else None
        if final_start_date or final_end_date:
            return [
                (
                    final_start_date.isoformat() if final_start_date else None,
                    final_end_date.isoformat() if final_end_date else None,
                )
            ]
        return []

    def get_date_intervals_original(self) -> list[tuple[str | None, str | None]]:
        """Get list of original date intervals."""
        final_original_start_date = min(self._all_original_start_dates) if self._all_original_start_dates else None
        final_original_end_date = max(self._all_original_end_dates) if self._all_original_end_dates else None
        if final_original_start_date or final_original_end_date:
            return [(final_original_start_date, final_original_end_date)]
        return []

    def get_more_than_two_dates(self) -> bool:
        """Get more than two dates flag."""
        return self._more_than_two_dates
