from schemas import ExtractedData

from .base import BaseAggregationStrategy


__all__ = ['MergeUniqueValuesStrategy']


class MergeUniqueValuesStrategy(BaseAggregationStrategy):
    """Strategy for merging unique values from all sources (client_name, ldmf_country)."""

    def __init__(self):
        self._client_names: set[str] = set()
        self._ldmf_countries: set[str] = set()

    def process(self, extracted_data: ExtractedData) -> None:
        """Merge unique values for client_name and ldmf_country."""
        if extracted_data.client_name:
            self._client_names.update(extracted_data.client_name)

        if extracted_data.ldmf_country:
            self._ldmf_countries.update(extracted_data.ldmf_country)

    def get_client_names(self) -> list[str]:
        """Get sorted list of unique client names."""
        return sorted(list(self._client_names))

    def get_ldmf_countries(self) -> list[str]:
        """Get sorted list of unique LDMF countries."""
        return sorted(list(self._ldmf_countries))
