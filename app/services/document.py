from io import BytesIO
import logging
from typing import Sequence
from uuid import UUID

from fastapi import UploadFile
from pydantic import HttpUrl

from config import settings
from constants.durable_functions import ProcessingStatus
from exceptions import EntityNotFoundError, MaximumDocumentsNumberExceeded, MaximumDocumentsSizeExceeded
from repositories import (
    DocumentBlobRepository,
    DocumentDbRepository,
    DocumentQueueRepository,
    ProcessingMessageRepository,
)
from schemas import DocumentCreationRequest, DocumentResponse, QualQueueMessage, Source


__all__ = ['DocumentService']

logger = logging.getLogger(__name__)
DEFAULT_PROMPT_NAME = 'prompt.txt'


class DocumentService:
    """Service for document-related business logic."""

    def __init__(
        self,
        document_db_repository: DocumentDbRepository,
        document_blob_repository: DocumentBlobRepository,
        document_queue_repository: DocumentQueueRepository,
        processing_message_repository: ProcessingMessageRepository,
    ):
        self.document_db_repository = document_db_repository
        self.document_blob_repository = document_blob_repository
        self.document_queue_repository = document_queue_repository
        self.processing_message_repository = processing_message_repository

    async def create_many(self, document_data: DocumentCreationRequest) -> list[DocumentResponse]:
        """
        Create multiple document records for an existing message and upload files to blob storage.

        Args:
            document_data: Data for creating the documents
            user_token: User access token for durable function API calls

        Returns:
            List of responses with the created document data

        Raises:
            MaximumDocumentsNumberExceeded: If there are too many documents
            MaximumDocumentsSizeExceeded: if the total size exceeds the limit
            EntityNotFoundError: If the conversation doesn't exist
            Exception: If there's an error creating the documents or uploading the files
        """
        try:
            # Clean up any corrupted documents before counting to ensure accurate limits
            await self.__cleanup_corrupted_documents_for_conversation(document_data.conversation_id)
            # Validate document limits
            await self.__validate_document_limits(document_data.conversation_id, document_data.files)
            files = document_data.files
            message_id = document_data.message_id
            signalr_user_id = document_data.conversation_id

            results = []
            document_urls = []

            for file in files:
                # Upload file directly using message_id
                file_url = await self.__upload(file, message_id)
                document = await self.document_db_repository.create(
                    message_public_id=message_id,
                    file_name=file.filename,  # type: ignore
                    file_size=file.size or 0,
                    file_type=file.content_type,  # type: ignore
                    file_url=str(file_url),
                )
                results.append(DocumentResponse.model_validate(document, from_attributes=True))
                document_urls.append(HttpUrl(file_url))

            # Send a single unified queue message for all documents
            if document_urls:
                queue_message = QualQueueMessage(
                    source=Source(documents=document_urls),
                    signal_r_connection_id=str(signalr_user_id),
                )
                await self.document_queue_repository.send_message(queue_message)

            return results

        except (EntityNotFoundError, MaximumDocumentsNumberExceeded, MaximumDocumentsSizeExceeded) as e:
            logger.error('Business logic error: %s', e)
            raise
        except Exception as e:  # pragma: no cover
            logger.error('Error creating documents: %s', e)
            raise

    async def delete_many(self, conversation_id: UUID) -> None:
        """
        Delete all documents associated with a conversation.

        Args:
            conversation_id: UUID of the conversation to delete documents from
        """
        try:
            file_info = await self.document_db_repository.get_file_info_for_deletion(conversation_id)

            blob_paths = []
            for file_name, message_id in file_info:
                blob_path = self.__generate_blob_path(file_name, message_id)
                blob_paths.append(blob_path)

            if blob_paths:
                await self.document_blob_repository.delete_many(blob_paths)

            await self.document_db_repository.delete_many(conversation_id)
            logger.info('Successfully deleted all documents for conversation ID "%s"', conversation_id)

        except Exception as e:  # pragma: no cover
            logger.error('Failed to delete documents for conversation ID %s: "%s"', conversation_id, e)
            raise

    async def __validate_document_limits(self, conversation_id: UUID, files: list[UploadFile]) -> None:
        """Validate document count and size limits."""
        doc_count = await self.document_db_repository.count_documents_by_conversation_id(conversation_id)
        max_docs = settings.document_storage.max_docs_per_conversation
        file_names = [file.filename for file in files]
        while file_names:
            if doc_count + 1 <= max_docs:
                file_names.pop(0)
                doc_count += 1
            else:
                raise MaximumDocumentsNumberExceeded(file_names.pop(0), max_docs)  # type: ignore

        total_size = await self.document_db_repository.get_total_size_by_conversation_id(conversation_id)
        new_size = sum(file.size or 0 for file in files)
        if total_size + new_size > settings.document_storage.max_conversation_size:
            raise MaximumDocumentsSizeExceeded(
                projected_size=total_size + new_size, max_docs_size=settings.document_storage.max_conversation_size
            )

    async def __cleanup_corrupted_documents_for_conversation(self, conversation_id: UUID) -> None:
        """
        Clean up corrupted document records for all messages in a conversation.

        This method finds all messages in the conversation that have corrupted documents
        and removes the corresponding QualDocument records from the database.

        Args:
            conversation_id: The conversation ID to clean up
        """
        try:
            # Get all file info for the conversation to find message IDs
            file_info = await self.document_db_repository.get_file_info_for_deletion(conversation_id)

            # Track unique message IDs that have files
            message_ids_with_files = set(message_id for _, message_id in file_info)

            total_removed = 0
            for message_id in message_ids_with_files:
                # Check if this message has corrupted documents
                statuses = await self.processing_message_repository.get_message_processing_statuses(message_id)

                if ProcessingStatus.DocumentIsCorrupted in statuses:
                    removed_count = await self.document_db_repository.remove_corrupted_documents_for_message(message_id)
                    total_removed += removed_count

            if total_removed > 0:
                logger.info(
                    'Cleaned up %d corrupted document records for conversation %s', total_removed, conversation_id
                )

        except Exception as e:
            logger.exception('Error cleaning up corrupted documents for conversation %s', conversation_id)
            raise e

    async def __upload(self, file: UploadFile, message_id: UUID, is_prompt: bool = False) -> str:
        """
        Upload a file to blob storage.

        Args:
            file: File to upload (already validated by schema)
            message_id: The message ID
            is_prompt: Whether the file is a prompt

        Returns:
            URL of the uploaded blob

        Raises:
            Exception: If upload fails
        """
        if is_prompt:
            blob_path = self.__generate_prompt_blob_path(message_id)
        else:
            blob_path = self.__generate_blob_path(file.filename, message_id)  # type: ignore

        try:
            content = await file.read()

            logger.info(
                "Uploading file '%s' (size: %d bytes, type: %s)",
                blob_path,
                file.size or 0,
                file.content_type,
            )

            return await self.document_blob_repository.upload_file(
                file_name=blob_path,
                content=content,
                content_type=file.content_type,
            )

        except Exception as e:  # pragma: no cover
            logger.error("Failed to upload file '%s': %s", blob_path, e)
            raise

    def __generate_blob_path(self, file_name: str, message_id: UUID) -> str:
        """
        Generate a path for storing a file in blob storage.

        Args:
            file: File to upload
            message_id: The message ID

        Returns:
            Formatted blob path
        """
        return f'uploads/{message_id}/{file_name}'

    def __generate_prompt_blob_path(self, message_id: UUID) -> str:
        """
        Generate a path for storing a prompt in blob storage.

        Args:
            message_id: The message ID

        Returns:
            Formatted blob path
        """
        return f'uploads-prompts/{message_id}/{DEFAULT_PROMPT_NAME}'

    async def create_prompt(self, content: str, signalr_user_id: UUID, message_id: UUID) -> str:
        """
        Create a prompt for a conversation.

        Args:
            content: The prompt content
            signalr_user_id: User ID for SignalR notifications (defaults to conversation_id)
            message_id: The message ID
            user_token: User access token for durable function API calls

        Returns:
            The created queue message id
        """

        # Create an in-memory file with the prompt content
        prompt_bytes = content.encode('utf-8')
        prompt_file = UploadFile(
            filename=DEFAULT_PROMPT_NAME,
            file=BytesIO(prompt_bytes),
        )
        prompt_file.size = len(prompt_bytes)  # type: ignore
        file_url = await self.__upload(prompt_file, message_id, is_prompt=True)

        # Send unified queue message for text prompt
        queue_message = QualQueueMessage(
            source=Source(text_prompt=HttpUrl(file_url)),
            signal_r_connection_id=str(signalr_user_id),
            tense=None,
        )
        queue_msg = await self.document_queue_repository.send_message(queue_message)

        return queue_msg.id

    async def create_combined_message(
        self, document_data: DocumentCreationRequest, text_content: str | None = None
    ) -> list[DocumentResponse]:
        """
        Create documents and/or text prompt and send a unified queue message.

        This method handles the unified queue approach where a single message can contain
        both text prompts and document attachments.

        Args:
            document_data: Data for creating the documents
            text_content: Optional text content to include as a prompt
            user_token: User access token for durable function API calls

        Returns:
            Tuple of (document responses, queue message id)

        Raises:
            MaximumDocumentsNumberExceeded: If there are too many documents
            MaximumDocumentsSizeExceeded: if the total size exceeds the limit
            EntityNotFoundError: If the conversation doesn't exist
            Exception: If there's an error creating the documents or uploading the files
        """
        try:
            document_responses = []
            document_urls = []
            text_prompt_url = None

            # Clean up any corrupted documents before counting to ensure accurate limits
            await self.__cleanup_corrupted_documents_for_conversation(document_data.conversation_id)
            # Handle file uploads if present
            if document_data.files:
                await self.__validate_document_limits(document_data.conversation_id, document_data.files)

                for file in document_data.files:
                    # Upload file directly using message_id
                    file_url = await self.__upload(file, document_data.message_id)
                    document = await self.document_db_repository.create(
                        message_public_id=document_data.message_id,
                        file_name=file.filename,  # type: ignore
                        file_size=file.size or 0,
                        file_type=file.content_type,  # type: ignore
                        file_url=str(file_url),
                    )
                    document_responses.append(DocumentResponse.model_validate(document, from_attributes=True))
                    document_urls.append(HttpUrl(file_url))

            # Handle text content if present
            if text_content and text_content.strip():
                # Create an in-memory file with the prompt content
                prompt_bytes = text_content.encode('utf-8')
                prompt_file = UploadFile(
                    filename=DEFAULT_PROMPT_NAME,
                    file=BytesIO(prompt_bytes),
                )
                prompt_file.size = len(prompt_bytes)  # type: ignore
                text_prompt_url = await self.__upload(prompt_file, document_data.message_id, is_prompt=True)

            # Send unified queue message if we have any content
            source = Source()
            if document_urls:
                source.documents = document_urls  # This is already HttpUrl list
            if text_prompt_url is not None:
                source.text_prompt = HttpUrl(text_prompt_url)

            queue_message = QualQueueMessage(
                source=source,
                signal_r_connection_id=str(document_data.conversation_id),
                tense=None,
            )
            await self.document_queue_repository.send_message(queue_message)

            return document_responses

        except (EntityNotFoundError, MaximumDocumentsNumberExceeded, MaximumDocumentsSizeExceeded) as e:
            logger.error('Business logic error: %s', e)
            raise
        except Exception as e:  # pragma: no cover
            logger.error('Error creating combined message: %s', e)
            raise

    @staticmethod
    def format_document_description(filenames: Sequence[str]) -> str:
        """
        Format document description for multiple files.

        Args:
            filenames: List of filenames to format

        Returns:
            Formatted description string for use in templates
        """
        if not filenames:
            return 'documents'

        # Remove file extensions and clean up names
        clean_names = [filename.split('.')[0] for filename in filenames]

        if len(clean_names) == 1:
            return f'a document related to "{clean_names[0]}"'
        elif len(clean_names) == 2:
            return f'documents related to "{clean_names[0]}" and "{clean_names[1]}"'
        else:
            # For 3 or more documents, use comma-separated list with "and" before the last item
            all_but_last = '", "'.join(clean_names[:-1])
            return f'documents related to "{all_but_last}", and "{clean_names[-1]}"'
