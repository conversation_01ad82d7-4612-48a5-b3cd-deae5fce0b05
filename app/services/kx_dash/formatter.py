from datetime import date
from typing import Any, Sequence

from constants.message import SystemReplyType


__all__ = ['kx_dash_message_formatter']


BOLD_HTML_OPEN = '<b>'
BOLD_HTML_CLOSE = '</b>'


class KXDashMessageFormatter:
    _DEFAULT_CLIENT_REPR = 'this client'
    _DEFAULT_ACTIVITY_REPR = 'activity'

    def __call__(
        self, activity: dict[str, Any], is_welcome_message: bool = False
    ) -> tuple[SystemReplyType, dict[str, Any]]:
        """
        Format the dash task selected message template with activity data.

        Args:
            activity: The dash task activity data

        Returns:
            Formatted message string
        """
        details: list[str] = []

        # Client info
        client_name = activity.get('client_name', self._DEFAULT_CLIENT_REPR)

        if client_name != self._DEFAULT_CLIENT_REPR:
            details.extend(self._get_detail('Client Name', client_name))

        # Location info
        if country := activity.get('country'):
            details.extend(self._get_detail('Lead Deloitte Member Firm/Country', country))

        # Activity info
        activity_name = activity.get('activity_name', self._DEFAULT_ACTIVITY_REPR)

        if activity_name != self._DEFAULT_ACTIVITY_REPR:
            details.extend(self._get_detail('Engagement Title', activity_name))

        # Dates
        if start_date := activity.get('engagement_start_date'):
            details.extend(self._get_detail('Start Date', start_date))
        if end_date := activity.get('engagement_end_date'):
            details.extend(self._get_detail('End Date', end_date))

        # Return the formatted template
        reply_type = (
            SystemReplyType.WELCOME_MESSAGE_WITH_ONE_DASH_SELECTED
            if is_welcome_message
            else SystemReplyType.DASH_TASK_SELECTED_TEMPLATE
        )
        kwargs = dict(
            activity_name=activity_name,
            client_name=client_name,
            details='\n'.join(details),
        )
        return reply_type, kwargs

    @staticmethod
    def _get_detail(label: str, value: Any = None, prefix: str = '• ') -> Sequence[str]:
        """
        Get detail representation
        """
        title = f'{BOLD_HTML_OPEN}{prefix}{label}{BOLD_HTML_CLOSE}'

        if value is None:  # For headers (when value is None), just add the label with a colon
            return (f'{title}:',)

        if value:  # For regular details with values
            if isinstance(value, date):
                return (f'{title}: {value.strftime("%-d %b %Y")}',)  # Date format of dash-tasks
            return (f'{title}: {value}',)

        return ()


kx_dash_message_formatter = KXDashMessageFormatter()
