import logging
from typing import Dict, List

from cachetools import TTLCache
import country_converter as coco
import pycountry
from pycountry.db import Country

from repositories.countries_blob import CountriesBlobRepository
from repositories.ldmf_countries import LDMFCountriesRepository
from schemas import CountryData


__all__ = ['LDMFCountryService']

logger = logging.getLogger(__name__)


class LDMFCountryService:
    """Service for LDMF country operations."""

    _cache: TTLCache = TTLCache(maxsize=1, ttl=60 * 60 * 1)  # Cache for 1 hour
    _CACHE_KEY = 'ldmf_countries'

    def __init__(
        self, ldmf_countries_repository: LDMFCountriesRepository, countries_blob_repository: CountriesBlobRepository
    ):
        self.ldmf_countries_repository = ldmf_countries_repository
        self.countries_blob_repository = countries_blob_repository

    async def list(self, token: str) -> Dict[str, CountryData]:
        """
        Get cached LDMF countries.

        Returns:
            Dictionary of LDMF countries.

        Raises:
            Exception: If an error occurs while getting cached LDMF countries.
        """
        try:
            logger.debug('Getting cached LDMF countries')
            countries = self._cache.get(self._CACHE_KEY)
            if countries is not None:
                logger.debug('Returning LDMF countries from cache')
                return countries

            ldmf_countries = await self.ldmf_countries_repository.list(token)
            countries = {ldmf_country.name: ldmf_country for ldmf_country in ldmf_countries}
            self._cache[self._CACHE_KEY] = countries

            logger.debug('Uploading LDMF countries to blob storage')
            await self.countries_blob_repository.upload_countries(countries)

            logger.debug('LDMF countries cached')
            return self._cache[self._CACHE_KEY]
        except Exception as e:
            logger.error('Error listing industries: %s', e)
            raise

    async def verify_ldmf_country(self, ldmf_country: str, token: str) -> List[str]:
        verified_country = await self._find_verified_country_name(ldmf_country, token)
        if verified_country:
            return [verified_country]

        countries = [value.strip() for value in ldmf_country.split(' and ')]
        verified_countries = []
        for country_part in countries:
            verified_country = await self._find_verified_country_name(country_part, token)
            if verified_country:
                verified_countries.append(verified_country)

        return verified_countries

    async def _find_verified_country_name(self, ldmf_country: str, token: str) -> str | None:
        cached_ldmf_countries = await self.list(token)
        if ldmf_country in cached_ldmf_countries:
            return ldmf_country

        lookup_methods = [self._perform_lookup, self._perform_lookup_coco, self._perform_search_fuzzy]
        for method in lookup_methods:
            country = method(ldmf_country)
            if country and country in cached_ldmf_countries:
                return country

        return None

    @staticmethod
    def _perform_lookup(ldmf_country: str) -> str | None:
        try:
            country: Country = pycountry.countries.lookup(ldmf_country)  # type: ignore
            return country.name
        except LookupError:
            pass
        return None

    @staticmethod
    def _perform_lookup_coco(ldmf_country: str) -> str | None:
        try:
            country = coco.convert(
                names=ldmf_country,
                to='name_short',
                not_found=None,
                enforce_list=False,
            )
            return country  # type: ignore
        except Exception:
            pass
        return None

    @staticmethod
    def _perform_search_fuzzy(ldmf_country: str) -> str | None:
        try:
            countries = pycountry.countries.search_fuzzy(ldmf_country)
            country: Country = countries[0]  # type: ignore

            # Add similarity threshold to prevent overly aggressive matching
            # Only accept matches that are reasonably similar (not just substring matches)
            input_lower = ldmf_country.lower().strip()
            country_lower = country.name.lower().strip()

            # If input is a substring of country name and country name is significantly longer,
            # it's likely a false positive (e.g., "Green" matching "Greenland")
            if (
                input_lower in country_lower
                and len(country_lower) > len(input_lower) + 3  # Allow some tolerance for abbreviations
                and not country_lower.startswith(input_lower + ' ')
            ):  # But not if it's a proper prefix
                return None

            # Reject if input length doesn't match country name length (for inputs > 3 chars)
            # This catches cases like "ermany" (6) vs "germany" (7) but allows "uk" vs "united kingdom"
            if len(input_lower) != len(country_lower) and len(input_lower) > 3:
                return None

            return country.name
        except LookupError:
            pass
        return None
