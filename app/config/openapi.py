from fastapi.routing import APIRoute

from .app_settings import settings


__all__ = ['setup_custom_openapi']


def setup_custom_openapi(app):
    original_openapi = app.openapi

    def custom_openapi():
        if not app.openapi_schema:
            app.openapi_schema = original_openapi()
        app.openapi_schema['components'] = app.openapi_schema.get('components', {})
        app.openapi_schema['components']['securitySchemes'] = {'HTTPBearer': {'type': 'http', 'scheme': 'bearer'}}
        app.openapi_schema['security'] = [{'HTTPBearer': []}]

        # NOTE: Drop the security schema for endpoints not requiring authentication
        for route in app.routes:
            if isinstance(route, APIRoute) and route.operation_id in settings.auth.auth_free_endpoints:
                for method in route.methods:
                    app.openapi_schema['paths'][route.path][method.lower()]['security'] = []

        return app.openapi_schema

    app.openapi = custom_openapi
