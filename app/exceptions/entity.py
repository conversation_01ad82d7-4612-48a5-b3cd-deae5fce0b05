from typing import Any

from .base import ApplicationError


__all__ = ['EntityNotFoundError', 'get_errormessage_404']


def get_errormessage_404(entity_type: Any, entity_id: Any) -> str:
    return f'{entity_type} with ID {entity_id} not found'


class EntityNotFoundError(ApplicationError):
    """Raised when an entity cannot be found by its identifier."""

    def __init__(self, entity_type: Any, entity_id: Any):
        self.entity_type = entity_type
        self.entity_id = entity_id
        super().__init__(get_errormessage_404(entity_type, entity_id))
