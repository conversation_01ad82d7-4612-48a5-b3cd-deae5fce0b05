from http import HTTPStatus
import json
import logging

import azure.durable_functions as df
import azure.functions as func

from durable_functions.application.config import settings


logger = logging.getLogger(__name__)

bp = df.Blueprint()


@bp.route(route='health', methods=['GET'])
@bp.durable_client_input(client_name='client')
async def health_check_http(req: func.HttpRequest, client: df.DurableOrchestrationClient) -> func.HttpResponse:
    """
    HTTP trigger to check health of the function app.
    """
    response = {
        'service': 'KX Quals AI Assistant Durable Functions',
        'healthy': True,
        'api_version': settings.version,
        'error': None,
    }
    return func.HttpResponse(body=json.dumps(response), status_code=HTTPStatus.OK, mimetype='application/json')
