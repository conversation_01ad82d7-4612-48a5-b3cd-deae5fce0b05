import logging

import azure.durable_functions as df
from azure.functions import QueueMessage

from durable_functions.application.config import settings
from durable_functions.application.queue_message_processor import QueueMessageProcessor


logger = logging.getLogger(__name__)

CONTENT_PROCESSING_QUEUE_NAME = settings.QUEUE_SETTINGS.CONTENT_PROCESSING_QUEUE_NAME
CONNECTION_ENV = settings.QUEUE_SETTINGS.CONNECTION_ENV

bp = df.Blueprint()


@bp.queue_trigger(arg_name='msg', queue_name=CONTENT_PROCESSING_QUEUE_NAME, connection=CONNECTION_ENV)
@bp.durable_client_input(client_name='client')
async def process_unified_queue(msg: QueueMessage, client: df.DurableOrchestrationClient) -> None:
    """
    Unified queue trigger to start document/prompt processing.

    Args:
        msg: Queue message
        client: Durable orchestration client

    Returns:
        None
    """
    processor = QueueMessageProcessor(client)
    msg_body = msg.get_body().decode('utf-8')
    await processor.process_message(msg_body)
