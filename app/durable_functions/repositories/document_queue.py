import json
import logging
from typing import Any

from azure.core.exceptions import ResourceExistsError
from azure.storage.queue import BinaryBase64DecodePolicy, BinaryBase64EncodePolicy, QueueMessage
from azure.storage.queue.aio import QueueClient


__all__ = ['DocumentQueueRepository']

logger = logging.getLogger(__name__)


class DocumentQueueRepository:
    """Document queue repository for durable functions result context."""

    def __init__(self, connection_string: str, queue_name: str):
        self.connection_string = connection_string
        self.queue_name = queue_name
        self.queue_client = self._create_queue_client(queue_name)

    def _create_queue_client(self, queue_name: str) -> QueueClient:
        return QueueClient.from_connection_string(
            self.connection_string,
            queue_name,
            message_encode_policy=BinaryBase64EncodePolicy(),
            message_decode_policy=BinaryBase64DecodePolicy(),
        )

    async def _ensure_queue_exists(self, queue_client: QueueClient, queue_name: str) -> None:
        try:
            await queue_client.create_queue()
            logger.debug("Queue '%s' created", queue_name)
        except ResourceExistsError:
            logger.debug("Queue '%s' already exists", queue_name)

    async def _send_message(self, content: dict[str, Any]) -> QueueMessage:
        async with self.queue_client:
            await self._ensure_queue_exists(self.queue_client, self.queue_name)

            message_bytes = json.dumps(content).encode('utf-8')
            try:
                msg = await self.queue_client.send_message(message_bytes)
                logger.info("Message sent to queue '%s': %s", self.queue_name, content)
                return msg
            except Exception:
                logger.exception("Error sending message to queue '%s'", self.queue_name)
                raise

    async def send_message(self, content: dict[str, Any]) -> QueueMessage:
        """
        Send a message to the configured queue.

        Args:
            content: A dictionary representing the JSON payload of the message.

        Returns:
            QueueMessage from the Azure Storage Queue
        """
        return await self._send_message(content)
