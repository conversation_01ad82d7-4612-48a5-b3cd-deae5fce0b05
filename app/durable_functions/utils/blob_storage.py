from contextlib import contextmanager
import json
import logging
from typing import Any, Dict, Iterator, List
import urllib.parse

from azure.core.exceptions import AzureError, ResourceExistsError, ResourceNotFoundError
from azure.storage.blob import BlobServiceClient, ContainerClient, ContentSettings

from durable_functions.application.config import settings


logger = logging.getLogger(__name__)


class BlobStorageError(Exception):
    """Base exception for blob storage operations."""

    pass


class BlobUploadError(BlobStorageError):
    """Exception raised when a blob upload fails."""

    pass


class BlobDownloadError(BlobStorageError):
    """Exception raised when a blob download fails."""

    pass


class BlobStorageHelper:
    """
    Helper class for Azure Blob Storage operations.

    This class provides methods for common blob storage operations such as
    uploading and downloading files, working with JSON data, and handling
    blob URLs.
    """

    def __init__(self, connection_string: str | None = None, container_name: str | None = None):
        """
        Initialize the blob storage helper.

        Args:
            connection_string: Azure Storage connection string. If None, uses the value from settings.
            container_name: Blob container name. If None, uses the value from settings.
        """
        self.connection_string = connection_string or settings.BLOB_STORAGE_SETTINGS.CONNECTION_STRING
        self.container_name = container_name or settings.BLOB_STORAGE_SETTINGS.DOCUMENT_CONTAINER_NAME
        self._service_client = None
        self._container_initialized = False

    def __enter__(self) -> 'BlobStorageHelper':
        """Enable context manager support."""
        return self

    def __exit__(self, *_) -> None:
        """Clean up resources when exiting context."""
        if self._service_client:
            self._service_client.close()
            self._service_client = None

    @property
    def service_client(self) -> BlobServiceClient:
        """
        Get the blob service client, creating it if it doesn't exist.

        Returns:
            BlobServiceClient instance
        """
        if not self._service_client:
            self._service_client = BlobServiceClient.from_connection_string(self.connection_string)
        return self._service_client

    @contextmanager
    def get_container_client(self) -> Iterator[ContainerClient]:
        """
        Get a container client as a context manager.

        Yields:
            ContainerClient for the configured container
        """
        container_client = self.service_client.get_container_client(self.container_name)
        yield container_client

    # Container operations

    def ensure_container_exists(self) -> None:
        """
        Ensure the container exists, creating it if necessary.

        This method is idempotent and can be called multiple times safely.
        """
        if self._container_initialized:
            return

        try:
            self.service_client.create_container(self.container_name)
            logger.info("Container '%s' created", self.container_name)
        except ResourceExistsError:
            logger.debug("Container '%s' already exists", self.container_name)

        self._container_initialized = True

    def blob_exists(self, blob_path: str) -> bool:
        """
        Check if a blob exists in the container.

        Args:
            blob_path: Path within the container

        Returns:
            True if the blob exists, False otherwise
        """
        self.ensure_container_exists()

        try:
            blob_client = self.service_client.get_blob_client(container=self.container_name, blob=blob_path)
            return blob_client.exists()
        except Exception:
            logger.exception('Error checking if blob exists')
            return False

    def list_blobs(self, prefix: str | None = None) -> List[str]:
        """
        List blobs in the container with an optional prefix.

        Args:
            prefix: Optional prefix to filter blobs

        Returns:
            List of blob names
        """
        self.ensure_container_exists()

        try:
            with self.get_container_client() as container_client:
                return [blob.name for blob in container_client.list_blobs(name_starts_with=prefix)]
        except Exception:
            logger.exception('Error listing blobs with prefix')
            return []

    # Upload operations

    def upload_file(self, blob_path: str, content: bytes, content_type: str | None = None) -> str:
        """
        Upload a file to blob storage.

        Args:
            blob_path: Path within the container
            content: File content as bytes
            content_type: MIME type of the file

        Returns:
            URL of the uploaded blob

        Raises:
            BlobUploadError: If the upload fails
        """
        if not blob_path:
            raise ValueError('Blob path cannot be empty')

        # Normalize the blob path to use forward slashes
        blob_path = blob_path.replace('\\', '/')

        self.ensure_container_exists()

        try:
            blob_client = self.service_client.get_blob_client(container=self.container_name, blob=blob_path)

            content_settings = ContentSettings(content_type=content_type) if content_type else None

            blob_client.upload_blob(
                content,
                content_settings=content_settings,
                overwrite=True,
            )

            logger.info("File '%s' uploaded successfully", blob_path)
            return blob_client.url

        except ValueError:
            logger.exception('Invalid input for blob')
            raise BlobUploadError('Invalid input')
        except AzureError:
            logger.exception('Azure error uploading blob')
            raise BlobUploadError('Azure error')
        except Exception:
            logger.exception('Failed to upload file')
            raise BlobUploadError('Unexpected error')

    def upload_json(self, blob_path: str, data: Dict[str, Any]) -> str:
        """
        Upload JSON data to blob storage.

        Args:
            blob_path: Path within the container
            data: Dictionary to be serialized as JSON

        Returns:
            URL of the uploaded blob

        Raises:
            BlobUploadError: If the upload fails
        """
        try:
            content = json.dumps(data, ensure_ascii=False).encode('utf-8')
            return self.upload_file(blob_path, content, 'application/json')
        except (TypeError, ValueError):
            logger.exception('JSON serialization error for')
            raise BlobUploadError('JSON serialization error')

    # Download operations

    def download_file(self, blob_path: str) -> bytes:
        """
        Download a file from blob storage.

        Args:
            blob_path: Path within the container

        Returns:
            File content as bytes

        Raises:
            BlobDownloadError: If the download fails
        """
        if not blob_path:
            raise ValueError('Blob path cannot be empty')

        # Normalize the blob path to use forward slashes
        blob_path = blob_path.replace('\\', '/')

        try:
            blob_client = self.service_client.get_blob_client(container=self.container_name, blob=blob_path)

            if not blob_client.exists():
                raise ResourceNotFoundError(f"Blob '{blob_path}' does not exist")

            return blob_client.download_blob().readall()

        except ResourceNotFoundError:
            logger.exception('Blob not found')
            raise BlobDownloadError(f'Blob not found: {blob_path}')
        except AzureError:
            logger.exception('Azure error downloading blob')
            raise BlobDownloadError('Azure error')
        except Exception:
            logger.exception('Failed to download file')
            raise BlobDownloadError('Unexpected error')

    def download_json(self, blob_path: str) -> Dict[str, Any]:
        """
        Download and parse JSON data from blob storage.

        Args:
            blob_path: Path within the container

        Returns:
            Parsed JSON data as dictionary

        Raises:
            BlobDownloadError: If the download fails or JSON parsing fails
        """
        try:
            content = self.download_file(blob_path)
            return json.loads(content)
        except json.JSONDecodeError:
            logger.exception('JSON parsing error for')
            raise BlobDownloadError('JSON parsing error')

    def get_blob_from_url(self, blob_url: str) -> bytes:
        """
        Download blob content from a URL.

        Args:
            blob_url: Full URL to the blob

        Returns:
            Blob content as bytes

        Raises:
            BlobDownloadError: If the download fails
        """
        if not blob_url:
            raise ValueError('Blob URL cannot be empty')

        decoded_blob_url = urllib.parse.unquote(blob_url)

        try:
            path = self.get_blob_path_from_url(decoded_blob_url)
            blob_client = self.service_client.get_blob_client(container=self.container_name, blob=path)

            if not blob_client.exists():
                raise ResourceNotFoundError(f"Blob at URL '{decoded_blob_url}' does not exist")

            return blob_client.download_blob().readall()
        except ResourceNotFoundError:
            logger.exception('Blob at URL not found')
            raise BlobDownloadError(f'Blob not found: {decoded_blob_url}')
        except AzureError as e:
            logger.error(f'Azure error downloading blob from URL: {e}')
            logger.exception('Azure error downloading blob from URL')
            raise BlobDownloadError('Azure error')
        except Exception:
            logger.exception('Failed to download blob from URL')
            raise BlobDownloadError('Unexpected error')

    def get_blob_path_from_url(self, blob_url: str) -> str:
        """
        Extract the blob path from a URL, keeping only the last three segments (uploads/{message_id}/{filename}).

        Args:
            blob_url: Full URL to the blob

        Returns:
            Blob path within the container
        """
        if not blob_url:
            raise ValueError('Blob URL cannot be empty')

        parsed = urllib.parse.urlparse(blob_url)
        path = parsed.path.lstrip('/')
        parts = path.split('/')

        # Skip container name if present and keep the last three parts
        if len(parts) >= 3:
            # If the container name is in the path, skip it
            if len(parts) > 0 and parts[0] == self.container_name and len(parts) >= 4:
                return '/'.join(parts[-3:])
            return '/'.join(parts[-3:])

        return path

    def find_and_download_json_chunks(self, message_ids: List[str]) -> Iterator[Dict[str, Any]]:
        """
        Finds all JSON chunk files for a given list of message IDs and downloads their content.

        Args:
            message_ids: A list of message IDs (strings) to search for chunks.

        Yields:
            The parsed JSON content of each found chunk file.

        Raises:
            BlobDownloadError: If a chunk file cannot be downloaded or parsed.
        """
        self.ensure_container_exists()
        with self.get_container_client() as container_client:
            for message_id in message_ids:
                prefix = f'chunks/{message_id}/'
                logger.info(f'Searching for JSON chunks with prefix: {prefix}')
                try:
                    # List blobs with the specific prefix
                    blobs = container_client.list_blobs(name_starts_with=prefix)
                    found_chunks = False
                    for blob in blobs:
                        if blob.name.endswith('.json'):
                            found_chunks = True
                            logger.info(f'Found JSON chunk: {blob.name}. Downloading...')
                            try:
                                blob_client = self.service_client.get_blob_client(
                                    container=self.container_name, blob=blob.name
                                )
                                content = blob_client.download_blob().readall()
                                yield json.loads(content)
                            except json.JSONDecodeError:
                                logger.error(f'Failed to parse JSON from chunk: {blob.name}')
                                raise BlobDownloadError(f'JSON parsing error for chunk: {blob.name}')
                            except AzureError as e:
                                logger.error(f'Azure error downloading chunk {blob.name}: {e}')
                                raise BlobDownloadError(f'Azure error downloading chunk: {blob.name}')
                            except Exception:
                                logger.exception(f'Unexpected error downloading chunk {blob.name}')
                                raise BlobDownloadError(f'Unexpected error downloading chunk: {blob.name}')
                    if not found_chunks:
                        logger.warning(f'No JSON chunks found for message ID: {message_id} under prefix {prefix}')
                except AzureError as e:
                    logger.error(f'Azure error listing blobs for message ID {message_id}: {e}')
                    raise BlobDownloadError(f'Azure error listing chunks for message ID: {message_id}')
                except Exception:
                    logger.exception(f'Unexpected error listing blobs for message ID {message_id}')
                    raise BlobDownloadError(f'Unexpected error listing chunks for message ID: {message_id}')
