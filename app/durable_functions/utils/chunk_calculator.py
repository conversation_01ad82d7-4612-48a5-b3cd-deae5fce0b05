from .llm_config import ModelTokenLimit


def get_chunks_size(
    system_prompt: int,
    token_limits: ModelTokenLimit,
    input_to_output: float,
    chunk_to_overlap: float,
    max_user_length_percent: int = 100,
) -> tuple[int, int, int, int]:
    effective_request_limit = int(token_limits.total * (max_user_length_percent / 100)) - system_prompt
    max_tokens = int(effective_request_limit * input_to_output)
    if token_limits.output and max_tokens > token_limits.output:
        max_tokens = token_limits.output

    text_total = effective_request_limit - max_tokens

    chunk_size = int(text_total * (1 - chunk_to_overlap))
    overlap_size = text_total - chunk_size

    return chunk_size, overlap_size, text_total, max_tokens
