import functools
import inspect
import logging


logger = logging.getLogger(__name__)


def _log_activity_event(activity_name: str, event_type: str, **kwargs):
    """Helper function to log activity events with a consistent format."""
    if event_type == 'started':
        logger.info(
            f"===> Activity '{activity_name}' started with args: {kwargs.get('args')}, kwargs: {kwargs.get('kwargs')}"
        )
    elif event_type == 'ended':
        logger.info(f"<=== Activity '{activity_name}' ended successfully. Result: {kwargs.get('result')}")
    elif event_type == 'failed':
        logger.exception(f"!!! Activity '{activity_name}' failed with an exception.")


def activity_logging_decorator(func):
    @functools.wraps(func)
    async def async_wrapper(*args, **kwargs):
        activity_name = func.__name__
        _log_activity_event(activity_name, 'started', args=args, kwargs=kwargs)
        try:
            result = await func(*args, **kwargs)
            _log_activity_event(activity_name, 'ended', result=result)
            return result
        except Exception:
            _log_activity_event(activity_name, 'failed')
            raise

    @functools.wraps(func)
    def sync_wrapper(*args, **kwargs):
        activity_name = func.__name__
        _log_activity_event(activity_name, 'started', args=args, kwargs=kwargs)
        try:
            result = func(*args, **kwargs)
            _log_activity_event(activity_name, 'ended', result=result)
            return result
        except Exception:
            _log_activity_event(activity_name, 'failed')
            raise

    if inspect.iscoroutinefunction(func):
        return async_wrapper
    else:
        return sync_wrapper
