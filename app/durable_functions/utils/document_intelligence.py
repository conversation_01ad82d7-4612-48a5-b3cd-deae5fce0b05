import io
import logging
from typing import Any, Dict, Optional

from azure.ai.documentintelligence import DocumentIntelligenceClient
from azure.ai.documentintelligence.models import AnalyzeResult
from azure.core.credentials import AzureKeyCredential
from azure.core.exceptions import HttpResponseError

from durable_functions.application.config import settings


logger = logging.getLogger(__name__)


class DocumentIntelligenceHelper:
    """Helper class for Azure Document Intelligence operations."""

    def __init__(self, endpoint: Optional[str] = None, key: Optional[str] = None):
        """
        Initialize the Document Intelligence helper.

        Args:
            endpoint: Document Intelligence endpoint URL
            key: Document Intelligence API key
        """
        self.endpoint = endpoint or settings.DOCUMENT_INTELLIGENCE_SETTINGS.ENDPOINT
        self.key = key or settings.DOCUMENT_INTELLIGENCE_SETTINGS.KEY
        self.model_name = settings.DOCUMENT_INTELLIGENCE_SETTINGS.MODEL_NAME

        if not self.endpoint or not self.key:
            raise ValueError('Document Intelligence endpoint and key must be provided')

        self.doc_int_client = DocumentIntelligenceClient(
            endpoint=self.endpoint, credential=AzureKeyCredential(self.key)
        )

    def extract_text_from_document(self, document_bytes: bytes) -> Dict[str, Any]:
        """
        Extract text from a document using Document Intelligence.

        Args:
            document_bytes: Document content as bytes

        Returns:
            Dictionary containing the extracted text and metadata
        """
        try:
            logger.info(f'Extracting text from document using Document Intelligence {type(document_bytes)}')
            # Create analyze request - simpler approach passing bytes directly
            poller = self.doc_int_client.begin_analyze_document(self.model_name, io.BytesIO(document_bytes))
            result: AnalyzeResult = poller.result()

            return {
                'text': result.content,
                'metadata': self._extract_metadata(result),
                'raw_analysis': self._serialize_analysis_result(result),
            }

        except HttpResponseError:
            logger.exception('Document Intelligence API error')
            raise
        except Exception:
            logger.exception('Error extracting text from document')
            raise

    def _extract_metadata(self, result: AnalyzeResult) -> Dict[str, Any]:
        """Extract metadata from analysis result."""
        return {
            'page_count': len(result.pages),
            'document_type': result.content_format,
            'languages': result.languages or [],
            'key_value_pairs': result.key_value_pairs or [],
        }

    def _serialize_analysis_result(self, result: AnalyzeResult) -> Dict[str, Any]:
        """Serialize the analysis result to a dictionary."""
        serialized = {
            'api_version': result.api_version,
            'content_type': result.content_format,
            'pages': [
                {
                    'page_number': page.page_number,
                    'width': page.width,
                    'height': page.height,
                    'unit': page.unit,
                    'lines': [{'content': line.content} for line in (page.lines or [])],
                }
                for page in result.pages
            ],
        }

        return serialized
