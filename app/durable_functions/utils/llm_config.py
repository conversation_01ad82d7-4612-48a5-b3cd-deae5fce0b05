from enum import StrEnum

from pydantic import BaseModel


class ModelTokenLimit(BaseModel):
    total: int
    output: int | None = None


class ModelDeployments(StrEnum):
    GPT_4_8K = 'gpt-4'
    GPT_4_32K = 'gpt-4-32k'
    GPT_4_128K = 'gpt-4-128k'
    GPT_4_O = 'gpt-4o'


class ModelSetting(BaseModel):
    deployment: ModelDeployments
    name: str
    limit: ModelTokenLimit


class ModelsSettings:
    MODELS: list[ModelSetting] = [
        ModelSetting(
            deployment=ModelDeployments.GPT_4_8K,
            name='GPT-4',
            limit=ModelTokenLimit(total=8192),
        ),
        ModelSetting(
            deployment=ModelDeployments.GPT_4_32K,
            name='GPT-4 32K',
            limit=ModelTokenLimit(total=32768),
        ),
        ModelSetting(
            deployment=ModelDeployments.GPT_4_128K,
            name='GPT-4 128K Output 4K',
            limit=ModelTokenLimit(total=128000, output=4096),
        ),
        ModelSetting(
            deployment=ModelDeployments.GPT_4_O,
            name='GPT-4o',
            limit=ModelTokenLimit(total=128000, output=4096),
        ),
    ]

    CHUNK_TO_OVERLAP: float = 0.1
    INPUT_TO_OUTPUT: float = 0.1

    MODELS_BY_DEPLOYMENT: dict[ModelDeployments, ModelSetting] = {model.deployment: model for model in MODELS}

    def get_settings(self, deployment: ModelDeployments = ModelDeployments.GPT_4_O) -> ModelSetting:
        if deployment not in self.MODELS_BY_DEPLOYMENT:
            raise ValueError(f'Unknown model deployment {deployment}.')

        return self.MODELS_BY_DEPLOYMENT[deployment]


# Example usage (commented for reference):
# current_chunk_tokens_count = calculate_tokens(
#     model_name=model_deployment, text=chunk
# )
# chunk_size, overlap_size, text_total, max_tokens = get_chunks_size(
#     system_prompt=empty_system_prompt_tokens,
#     token_limits=ModelTokenLimit(total=int(current_chunk_tokens_count * 0.8)),
#     input_to_output=0.2,
#     chunk_to_overlap=0.1,
#     max_user_length_percent=95,
# )
