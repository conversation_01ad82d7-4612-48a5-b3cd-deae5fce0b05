import re


def parse_blob_url(blob_url: str, is_prompt: bool = False) -> tuple[str, str]:
    # Example: .../uploads/<message_id>/<file_name>
    if is_prompt:
        match = re.search(r'/uploads-prompts/([a-f0-9\-]+)/([^/]+)$', blob_url)
    else:
        match = re.search(r'/uploads/([a-f0-9\-]+)/([^/]+)$', blob_url)

    if not match:
        raise ValueError('Invalid blob_url format')
    message_id, file_name = match.groups()
    return message_id, file_name
