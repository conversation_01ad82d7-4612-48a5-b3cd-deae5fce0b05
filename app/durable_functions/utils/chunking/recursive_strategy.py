import logging
from typing import Any, Dict, List

from langchain_text_splitters import RecursiveCharacterTextSplitter

from .base import BaseChunkingStrategy
from .config import settings


logger = logging.getLogger(__name__)


class RecursiveChunkingStrategy(BaseChunkingStrategy):
    """Chunking strategy using LangChain's RecursiveCharacterTextSplitter with tiktoken."""

    def __init__(
        self,
        chunk_size: int = settings.CHUNK_SIZE,
        chunk_overlap: int = settings.CHUNK_OVERLAP,
        encoding_name: str = settings.ENCODING_NAME,
        separators: List[str] = settings.SEPARATORS,
    ):
        """
        Initialize the LangChain chunking strategy.

        Args:
            chunk_size: Maximum size of each chunk in tokens
            chunk_overlap: Number of tokens to overlap between chunks
            encoding_name: Name of the tiktoken encoding to use
            separators: List of separators to use for splitting text
        """
        super().__init__(chunk_size=chunk_size, chunk_overlap=chunk_overlap)
        self.encoding_name = encoding_name
        self.separators = separators

        # Create the text splitter using tiktoken encoder
        self.text_splitter = RecursiveCharacterTextSplitter.from_tiktoken_encoder(
            encoding_name=encoding_name,
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            separators=self.separators,
            is_separator_regex=False,
        )

    def chunk_document(self, document_content: str, metadata: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Chunk a document using LangChain's RecursiveCharacterTextSplitter.

        Args:
            document_content: The text content of the document
            metadata: Additional information about the document

        Returns:
            List of chunks, each containing the text and metadata
        """
        logger.info(f'Chunking document using LangChain with chunk size {self.chunk_size}')

        # Split the text into chunks
        text_chunks = self.text_splitter.split_text(document_content)

        # Create chunk dictionaries
        chunks = []
        for i, chunk_text in enumerate(text_chunks):
            chunks.append(self._create_chunk(chunk_text, metadata, i))

        return chunks

    def _get_token_count(self, text: str) -> int:
        """Get the accurate number of tokens in the text using tiktoken."""
        try:
            # Use the text splitter's built-in token counter
            return self.text_splitter._length_function(text)
        except Exception as e:
            logger.warning(f'Failed to count tokens: {e}')
            # Fallback to the base implementation
            return super()._get_token_count(text)
