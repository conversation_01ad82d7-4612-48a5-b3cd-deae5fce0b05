from pydantic import BaseModel
import tiktoken

from durable_functions.utils.llm_config import ModelsSettings


__all__ = ['settings']

default_llm_model = ModelsSettings().get_settings()


class ChunkingSettings(BaseModel):
    CHUNK_SIZE: int = 8096
    CHUNK_OVERLAP: int = 512
    ENCODING_NAME: str = tiktoken.encoding_for_model(str(default_llm_model.deployment)).name
    SEPARATORS: list[str] = ['\n\n', '\n', '. ', ' ', '']


settings = ChunkingSettings()
