import abc
import logging
from typing import Any, Dict, List
import uuid


logger = logging.getLogger(__name__)


class BaseChunkingStrategy(abc.ABC):
    """Base abstract class for all chunking strategies."""

    def __init__(self, chunk_size: int, chunk_overlap: int, **kwargs):
        """
        Initialize the base chunking strategy.

        Args:
            chunk_size: Maximum size of each chunk in tokens
            chunk_overlap: Number of tokens to overlap between chunks
            **kwargs: Additional strategy-specific parameters
        """
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.strategy_name = type(self).__name__

    @abc.abstractmethod
    def chunk_document(self, document_content: str, metadata: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Chunk a document using the specific strategy.

        Args:
            document_content: The text content of the document
            metadata: Additional information about the document

        Returns:
            List of chunks, each containing the text and metadata
        """
        pass

    def _get_token_count(self, text: str) -> int:
        """
        Get the approximate number of tokens in the text.

        This is a fallback method that should be overridden by subclasses
        if they have a more accurate way to count tokens.
        """
        # Simple word-based approximation
        return len(text.split())

    def _create_chunk(self, text: str, metadata: Dict[str, Any], index: int) -> Dict[str, Any]:
        """Create a chunk dictionary with text and metadata."""
        return {
            'chunk_id': str(uuid.uuid4()),
            'chunk_index': index,
            'text': text.strip(),
            'metadata': metadata,
            'chunking_strategy': self.strategy_name,
            'token_count': self._get_token_count(text),
        }
