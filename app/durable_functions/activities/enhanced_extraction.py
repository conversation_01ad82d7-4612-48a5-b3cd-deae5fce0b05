"""Enhanced extraction activities for comprehensive qual field processing."""

import logging

import azure.durable_functions as df
from openai import AsyncAzureOpenAI

from constants.durable_functions import ActivityName
from durable_functions.application.config import settings
from durable_functions.repositories.openai import OpenAIRepository
from durable_functions.utils.activity_logger import activity_logging_decorator
from durable_functions.utils.models import FinalExtractionDataResults

from .models import (
    BaseExtractionResult,
    EnhancedExtractionActivityInput,
    EnhancedExtractionActivityOutput,
    SummarizeOtherFieldActivityInput,
    SummarizeOtherFieldActivityOutput,
)


logger = logging.getLogger(__name__)

bp = df.Blueprint()


async def _summarize_other_field(
    input_data: SummarizeOtherFieldActivityInput, openai_repo: OpenAIRepository
) -> SummarizeOtherFieldActivityOutput:
    logger.info(f'Starting other field summarization for conversation {input_data.conversation_id}')

    other_contents = [chunk.other for chunk in input_data.chunks_extracted if chunk.other]
    summarized_content = await openai_repo.summarize_other_field(other_contents)

    result = SummarizeOtherFieldActivityOutput(
        conversation_id=input_data.conversation_id,
        summarized_other_content=summarized_content,
        chunk_count=len(input_data.chunks_extracted),
        processed_chunks=len(other_contents),
    )

    logger.info(f'Completed other field summarization: {result.processed_chunks}/{result.chunk_count} chunks processed')
    return result


@bp.activity_trigger('input_data', ActivityName.SummarizeOtherField)
@activity_logging_decorator
async def summarize_other_field_activity(
    input_data: SummarizeOtherFieldActivityInput,
) -> SummarizeOtherFieldActivityOutput:
    """
    Activity to summarize 'other' field content across all chunks for enhanced extraction.

    Args:
        input_data: SummarizeOtherFieldActivityInput object

    Returns:
        SummarizeOtherFieldActivityOutput object
    """
    try:
        async with AsyncAzureOpenAI(
            azure_endpoint=settings.openai.endpoint,
            api_key=settings.openai.key,
            api_version=settings.openai.api_version,
        ) as client:
            openai_repo = OpenAIRepository(client=client)
            return await _summarize_other_field(input_data, openai_repo)

    except Exception as e:
        logger.exception(f'Error in summarize_other_field_activity: {e}')
        raise


async def _enhanced_extraction(
    extraction_input: EnhancedExtractionActivityInput, openai_repo: OpenAIRepository
) -> EnhancedExtractionActivityOutput:
    extracted_fields = {}
    for task in extraction_input.tasks:
        logger.info(f'Executing enhanced extraction for field: {task.field_name}')
        structured_response = await openai_repo.extract_structured_data(
            system_prompt=task.system_prompt,
            user_prompt=task.user_prompt,
            response_model=BaseExtractionResult,
        )
        if structured_response and structured_response.value:
            extracted_fields[task.field_name] = structured_response.value

    logger.info(f'Enhanced extraction completed for conversation: {extraction_input.conversation_id}')
    return EnhancedExtractionActivityOutput(
        conversation_id=extraction_input.conversation_id,
        source=extraction_input.source,
        extracted_data=FinalExtractionDataResults.model_validate(extracted_fields),
    )


@bp.activity_trigger(input_name='extraction_input', activity=ActivityName.EnhancedExtraction)
@activity_logging_decorator
async def enhanced_extraction_activity(
    extraction_input: EnhancedExtractionActivityInput,
) -> EnhancedExtractionActivityOutput:
    try:
        async with AsyncAzureOpenAI(
            azure_endpoint=settings.openai.endpoint,
            api_key=settings.openai.key,
            api_version=settings.openai.api_version,
        ) as client:
            openai_repo = OpenAIRepository(client=client)
            return await _enhanced_extraction(extraction_input, openai_repo)
    except Exception:
        logger.exception('Error in enhanced_extraction_activity')
        raise
