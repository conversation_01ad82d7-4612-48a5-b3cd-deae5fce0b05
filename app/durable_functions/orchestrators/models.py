from typing import Any

from constants.durable_functions import EventType
from durable_functions.utils import DFBaseModel


class CorruptedDocument(DFBaseModel):
    file_name: str
    error: str


class CorruptedDocumentsErrorPayload(DFBaseModel):
    message_id: str
    docs: list[CorruptedDocument]


class ProcessDocumentInput(DFBaseModel):
    blob_url: str
    signalr_user_id: str
    is_part_of_unified_processing: bool = False


class BaseProcessingOrchestratorOutput(DFBaseModel):
    message_id: str
    file_name: str
    status: str


class DeferredCorruptionEvent(DFBaseModel):
    """Model for corruption events that need to be sent later in unified processing"""

    event_type: EventType
    data: dict[str, Any]
    signalr_user_id: str


class DocumentProcessingOrchestratorOutput(BaseProcessingOrchestratorOutput):
    extraction_url: str | None = None
    chunk_count: int | None = None
    chunk_urls: list[dict[str, Any]] | None = None
    signalr_user_id: str
    metadata: dict[str, Any] | None = None
    deferred_corruption_event: DeferredCorruptionEvent | None = None


class DocumentProcessingOrchestratorOutputFailed(BaseProcessingOrchestratorOutput):
    error: str
    deferred_corruption_event: DeferredCorruptionEvent | None = None


class ProcessPromptInput(DFBaseModel):
    prompt_url: str
    signalr_user_id: str
    is_part_of_unified_processing: bool = False


class UnifiedProcessingInput(DFBaseModel):
    text_prompt: str | None = None
    documents: list[str] | None = None
    signalr_user_id: str


class EnhancedProcessingInput(DFBaseModel):
    message_ids: list[str]
    signalr_user_id: str
    tense: str | None = None


class UnifiedProcessingOutput(DFBaseModel):
    message_id: str
    status: str
    aggregated_results: dict[str, Any] | None = None
    blob_url: str | None = None
    signalr_user_id: str


class UnifiedProcessingOutputFailed(DFBaseModel):
    message_id: str | None = None
    status: str
    error: str
    signalr_user_id: str
