"""Progress tracker entity for Azure Durable Functions.

This module contains the progress tracker entity that maintains state for
long-running operations and provides centralized progress tracking across
multiple parallel tasks.
"""

import logging
from typing import Any

import azure.durable_functions as df

from constants.durable_functions import EntityOperation


logger = logging.getLogger(__name__)
bp = df.Blueprint()

# Constants for progress calculation
MAX_PROGRESS_PERCENT = 99


@bp.entity_trigger(context_name='context')
def progress_tracker_entity(context: df.DurableEntityContext) -> None:
    """A Durable Entity to track the progress of a long-running operation.

    This entity maintains the state of a process that involves multiple steps,
    allowing for centralized and monotonic progress tracking even when tasks
    are executed in parallel.

    State:
        total_items (int): The total number of items to be processed.
        processed_items (int): The number of items that have been completed.
        message_ids (list[str]): A list of message_ids associated with the operation.
        last_reported_percent (int): The last reported progress percentage.

    Operations:
        initialize: Sets up the initial state with total items and message IDs.
        increment: Increments the count of processed items and returns the new
                   progress percentage and associated message IDs if the
                   percentage has changed.
        reset: Clears the entity's state.
    """
    state = context.get_state(_get_default_state)
    operation = context.operation_name

    if operation == EntityOperation.INITIALIZE:
        _handle_initialize_operation(context, state)
    elif operation == EntityOperation.INCREMENT:
        _handle_increment_operation(context, state)
    elif operation == EntityOperation.RESET:
        _handle_reset_operation(context)
    else:
        logger.warning(f'Unknown operation: {operation}')


def _get_default_state() -> dict[str, Any]:
    """Get the default state for the progress tracker entity.

    Returns:
        dict: Default state with all fields initialized to appropriate values.
    """
    return {
        'total_items': 0,
        'processed_items': 0,
        'message_ids': [],
        'last_reported_percent': -1,
    }


def _handle_initialize_operation(context: df.DurableEntityContext, state: dict[str, Any]) -> None:
    """Handle the initialize operation for the progress tracker entity.

    Args:
        context: The durable entity context.
        state: Current entity state (unused in this operation as we create new state).
    """
    payload = context.get_input()
    new_state = {
        'total_items': payload.get('total_items', 0),
        'processed_items': 0,
        'message_ids': payload.get('message_ids', []),
        'last_reported_percent': -1,
    }
    context.set_state(new_state)
    logger.info(f'Progress tracker initialized with {new_state["total_items"]} total items')


def _handle_increment_operation(context: df.DurableEntityContext, state: dict[str, Any]) -> None:
    """Handle the increment operation for the progress tracker entity.

    Args:
        context: The durable entity context.
        state: Current entity state to be updated.
    """
    state['processed_items'] += 1

    current_progress = _calculate_progress(state['processed_items'], state['total_items'])

    if current_progress > state.get('last_reported_percent', -1):
        state['last_reported_percent'] = current_progress
        result = {'percent': current_progress, 'message_ids': state['message_ids']}
        context.set_result(result)
        logger.debug(f'Progress updated: {current_progress}% ({state["processed_items"]}/{state["total_items"]})')
    else:
        context.set_result(None)

    context.set_state(state)


def _handle_reset_operation(context: df.DurableEntityContext) -> None:
    """Handle the reset operation for the progress tracker entity.

    Args:
        context: The durable entity context.
    """
    context.set_state(_get_default_state())
    logger.info('Progress tracker reset to default state')


def _calculate_progress(processed_items: int, total_items: int) -> int:
    """Calculate the current progress percentage.

    Args:
        processed_items: Number of items that have been processed.
        total_items: Total number of items to be processed.

    Returns:
        int: Progress percentage (0-99).
    """
    if total_items <= 0:
        return 0

    progress_fraction = processed_items / total_items
    return min(int(round(progress_fraction * MAX_PROGRESS_PERCENT)), MAX_PROGRESS_PERCENT)
