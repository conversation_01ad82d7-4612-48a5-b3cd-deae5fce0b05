import logging

from azure.core.exceptions import ResourceExistsError
from azure.storage.queue import BinaryBase64D<PERSON>odePolicy, BinaryBase64EncodePolicy, QueueMessage
from azure.storage.queue.aio import QueueClient

from schemas import QualQueueMessage


__all__ = ['DocumentQueueRepository']

logger = logging.getLogger(__name__)


class DocumentQueueRepository:
    """a copy of Document queue repository for durable functions context."""

    def __init__(self, connection_string: str, queue_name: str) -> None:
        self.connection_string = connection_string
        self.queue_name = queue_name
        self.queue_client = self._create_queue_client(queue_name)

    def _create_queue_client(self, queue_name: str) -> QueueClient:
        return QueueClient.from_connection_string(
            self.connection_string,
            queue_name,
            message_encode_policy=BinaryBase64EncodePolicy(),
            message_decode_policy=BinaryBase64DecodePolicy(),
        )

    async def _ensure_queue_exists(self, queue_client: QueueClient, queue_name: str) -> None:
        try:
            await queue_client.create_queue()
            logger.debug("Queue '%s' created", queue_name)
        except ResourceExistsError:
            logger.debug("Queue '%s' already exists", queue_name)

    async def _send_message(self, message_bytes: bytes) -> QueueMessage:
        async with self.queue_client:
            await self._ensure_queue_exists(self.queue_client, self.queue_name)
            try:
                msg = await self.queue_client.send_message(message_bytes)
                logger.info("Message sent to queue '%s': %s", self.queue_name, message_bytes.decode('utf-8'))
                return msg
            except Exception:
                logger.exception("Error sending message to queue '%s'", self.queue_name)
                raise

    async def send_message(self, message_input: QualQueueMessage) -> QueueMessage:
        """
        Send a unified message to the default queue.

        This method handles both text prompts and document attachments in a single message,
        supporting the unified queue approach where users can send:
        - Text-only messages
        - File-only messages
        - Combined text + file messages

        Args:
            message_input: QualQueueMessage containing source data and SignalR connection ID

        Returns:
            QueueMessage from the Azure Storage Queue
        """
        message_bytes = message_input.model_dump_json().encode('utf-8')
        try:
            msg = await self._send_message(message_bytes)
            logger.info("Message sent to queue '%s': %s", self.queue_name, message_input.model_dump())
            return msg
        except Exception:
            logger.exception("Error sending message to queue '%s'", self.queue_name)
            raise
