import json
import logging
from typing import Dict

from azure.core.exceptions import ResourceExistsError
from azure.storage.blob.aio import BlobServiceClient

from schemas import CountryData


__all__ = ['CountriesBlobRepository']

logger = logging.getLogger(__name__)


class CountriesBlobRepository:
    """Repository for Azure Blob Storage operations using async client."""

    def __init__(self, connection_string: str, container_name: str):
        self.connection_string = connection_string
        self.container_name = container_name
        self._initialized = False

    def _get_service_client(self) -> BlobServiceClient:
        """Get an async blob service client."""
        return BlobServiceClient.from_connection_string(self.connection_string)

    async def initialize(self) -> None:
        """Initialize the container asynchronously."""
        if self._initialized:
            return

        async with self._get_service_client() as service_client:
            container_client = service_client.get_container_client(self.container_name)

            try:
                await container_client.create_container()
                logger.info("Container '%s' created", self.container_name)
            except ResourceExistsError:
                logger.debug("Container '%s' already exists", self.container_name)

            self._initialized = True

    async def upload_countries(self, countries: Dict[str, CountryData]) -> None:
        """
        Upload a file to blob storage asynchronously.

        Args:
            file_name: Name of the file
            content: File content as bytes
            content_type: MIME type of the file

        Returns:
            URL of the uploaded blob

        Raises:
            Exception: If upload fails
        """
        await self.initialize()

        try:
            async with self._get_service_client() as service_client:
                container_client = service_client.get_container_client(self.container_name)
                blob_client = container_client.get_blob_client('countries.json')
                countries_list = [country.model_dump() for country in countries.values()]
                countries_json = json.dumps({'countries': countries_list})

                result = await blob_client.upload_blob(countries_json, overwrite=True)
                logger.info('Countries uploaded successfully: %s', result)

        except Exception as e:  # pragma: no cover
            logger.error('Failed to upload countries: %s', e)
            raise
