import logging
from typing import Sequence

from azure.core.exceptions import ResourceExistsError
from azure.storage.blob import ContentSettings
from azure.storage.blob.aio import BlobServiceClient

from config import settings


__all__ = ['DocumentBlobRepository']

logger = logging.getLogger(__name__)


class DocumentBlobRepository:
    """Repository for Azure Blob Storage operations using async client."""

    def __init__(self, connection_string: str):
        self.connection_string = connection_string
        self.container_name = settings.document_storage.container_name
        self._initialized = False

    async def initialize(self) -> None:
        """Initialize the container asynchronously."""
        if self._initialized:
            return

        async with self._get_service_client() as service_client:
            container_client = service_client.get_container_client(self.container_name)

            try:
                await container_client.create_container()
                logger.info("Container '%s' created", self.container_name)
            except ResourceExistsError:
                logger.debug("Container '%s' already exists", self.container_name)

            self._initialized = True

    async def upload_file(self, file_name: str, content: bytes, content_type: str | None = None) -> str:
        """
        Upload a file to blob storage asynchronously.

        Args:
            file_name: Name of the file
            content: File content as bytes
            content_type: MIME type of the file

        Returns:
            URL of the uploaded blob

        Raises:
            Exception: If upload fails
        """
        await self.initialize()

        try:
            async with self._get_service_client() as service_client:
                container_client = service_client.get_container_client(self.container_name)
                blob_client = container_client.get_blob_client(file_name)
                content_settings = ContentSettings(content_type=content_type) if content_type else None

                await blob_client.upload_blob(
                    content,
                    content_settings=content_settings,
                    overwrite=True,
                )

                logger.info("File '%s' uploaded successfully", file_name)
                return blob_client.url

        except Exception as e:  # pragma: no cover
            logger.error("Failed to upload file '%s': %s", file_name, e)
            raise

    def _get_service_client(self) -> BlobServiceClient:
        """Get an async blob service client."""
        return BlobServiceClient.from_connection_string(self.connection_string)

    async def delete_many(self, blob_paths: Sequence[str]) -> None:
        """
        Delete multiple blobs from storage.

        Args:
            blob_paths: List of blob paths to delete
        """
        await self.initialize()

        try:
            async with self._get_service_client() as service_client:
                container_client = service_client.get_container_client(self.container_name)
                deleted_count = 0

                for blob_path in blob_paths:
                    try:
                        # Delete the blob
                        await container_client.delete_blob(blob_path)
                        deleted_count += 1
                        logger.info("Deleted blob '%s'", blob_path)
                    except Exception as e:
                        logger.error("Failed to delete blob '%s': %s", blob_path, e)
                        # Continue with other blobs even if one fails

                logger.info('Successfully deleted %d of %d blobs', deleted_count, len(blob_paths))
        except Exception as e:  # pragma: no cover
            logger.error('Failed to initialize blob service for deletion: %s', e)
            raise
