import logging
from typing import Any

from backoff import expo, on_exception
from openai import AsyncAzureOpenAI, RateLimitError
from openai._types import NOT_GIVEN, NotGiven
from openai.types.chat import ChatCompletion, ChatCompletionMessageParam

from config import settings


__all__ = ['OpenAIRepository']


logger = logging.getLogger(__name__)


openai_client = AsyncAzureOpenAI(
    api_key=settings.openai.key,
    api_version=settings.openai.api_version,
    azure_endpoint=settings.openai.endpoint,
)


rate_limit_backoff = on_exception(
    expo,
    RateLimitError,
    max_tries=settings.http_client.backoff.max_retries,
    giveup=lambda e: not isinstance(e, RateLimitError),
    logger=logger,
    base=settings.http_client.backoff.backoff_base,
    factor=settings.http_client.backoff.backoff_factor,
)


class OpenAIRepository:
    """Repository for interacting with Azure OpenAI API."""

    def __init__(self, client: AsyncAzureOpenAI = openai_client) -> None:
        """Initialize the OpenAI service with settings from the config."""
        self.client = client

    @rate_limit_backoff
    async def generate_chat_completion(
        self,
        messages: list[ChatCompletionMessageParam],
        temperature: float,
        model: str = settings.openai.model,
        response_format: Any | NotGiven = NOT_GIVEN,
    ) -> ChatCompletion:  # pragma: no cover
        """
        Generate a chat completion from OpenAI.

        Args:
            messages: List of messages to send to the API.
            temperature: Temperature parameter for controlling randomness.
            model: The specific model to use for the completion.
            response_format: pydantic BaseModel to parse the response into.

        Returns:
            The chat completion response converted to the specified pydantic BaseModel.
        """

        try:
            logger.info('Calling OpenAI API with %d messages', len(messages))
            response = await self.client.beta.chat.completions.parse(
                model=model,
                messages=messages,
                temperature=temperature,
                response_format=response_format,
            )

            message = response.choices[0].message

            if message.parsed:
                return message.parsed

            raise RuntimeError(f'No valid response from model, refusal: {message.refusal}')

        except Exception:
            logger.exception('Error calling OpenAI API. Response format: %s', str(response_format))
            raise

    @rate_limit_backoff
    async def generate_text_completion(
        self,
        messages: list[ChatCompletionMessageParam],
        temperature: float,
        max_completion_tokens: int,
        model: str = settings.openai.model,
    ) -> str:  # pragma: no cover
        """
        Generate a plain text completion from OpenAI.

        Args:
            messages: List of messages to send to the API.
            temperature: Temperature parameter for controlling randomness.
            max_completion_tokens: Maximum number of tokens in the response.
            model: The specific model to use for the completion.

        Returns:
            The plain text response from the model.
        """

        try:
            logger.info('Calling OpenAI API for text completion with %d messages', len(messages))
            response = await self.client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature,
                max_completion_tokens=max_completion_tokens,
            )

            message = response.choices[0].message

            if message.content:
                return message.content

            raise RuntimeError(f'No valid response from model, refusal: {message.refusal}')

        except Exception:
            logger.exception('Error calling OpenAI API for text completion')
            raise
