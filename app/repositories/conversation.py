import logging
from uuid import UUID

from sqlalchemy import delete, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from constants.extracted_data import ConversationState
from exceptions.entity import EntityNotFoundError
from models import QualConversation
from schemas import ConversationCreationRequest
from schemas.confirmed_data import ConfirmedData


__all__ = ['ConversationRepository']


logger = logging.getLogger(__name__)


class ConversationRepository:
    """Repository for conversation-related database operations."""

    def __init__(self, db_session: AsyncSession):
        self.db_session = db_session

    async def create(
        self, conversation_data: ConversationCreationRequest, user_id: UUID, user_name: str
    ) -> QualConversation:
        """
        Create a new conversation in the database.

        Args:
            conversation_data: Data for creating the conversation

        Returns:
            The created conversation
        """
        new_conversation = QualConversation(
            DashActivityId=conversation_data.dash_activity_id,
            IsCompleted=False,
            CreatedById=user_id,
            CreatedByName=user_name,
        )

        self.db_session.add(new_conversation)
        await self.db_session.flush()

        return new_conversation

    async def get(self, public_id: UUID) -> QualConversation | None:
        """
        Get a conversation by its public ID.

        Args:
            public_id: The public ID of the conversation

        Returns:
            The conversation itself, if found, None otherwise
        """
        query = select(QualConversation).where(QualConversation.PublicId == public_id)
        result = await self.db_session.execute(query)
        return result.scalar_one_or_none()

    async def get_internal_id(self, public_id: UUID) -> int | None:
        """
        Get a conversation internal ID by its public ID.

        Args:
            public_id: The public ID of the conversation

        Returns:
            The conversation internal ID if found, None otherwise
        """
        return (
            await self.db_session.execute(select(QualConversation.Id).where(QualConversation.PublicId == public_id))
        ).scalar_one_or_none()

    async def exists(self, public_id: UUID) -> bool:
        """
        Check if a conversation with specified public ID exists.

        Args:
            public_id: The public ID of the conversation

        Returns:
            True if the conversation is found, False otherwise
        """
        query = select(1).where(QualConversation.PublicId == public_id)
        result = await self.db_session.execute(query)
        return result.scalar() is not None

    async def delete(self, public_id: UUID) -> None:
        """
        Delete a conversation by its public ID.

        Args:
            public_id: The public ID of the conversation

        Returns:
            None
        """
        logger.debug('Deleting conversation record with ID: %s', public_id)
        query = delete(QualConversation).where(QualConversation.PublicId == public_id)
        await self.db_session.execute(query)

    async def get_owner_id(self, conversation_id: UUID) -> UUID | None:
        """
        Get an owner ID for the conversation.

        Args:
            conversation_id: The public ID of the conversation

        Returns:
            The owner ID if found, None otherwise
        """
        query = select(QualConversation.CreatedById).where(QualConversation.PublicId == conversation_id)
        result = await self.db_session.execute(query)
        return result.scalar_one_or_none()

    async def update_state(self, public_id: UUID, state: ConversationState) -> None:
        """
        Update the state for a conversation.

        Args:
            public_id: The public ID of the conversation
            state: The new conversation state

        Returns:
            None
        """
        logger.debug('Updating state for conversation ID: %s to %s', public_id, state)
        query = update(QualConversation).where(QualConversation.PublicId == public_id).values(State=state)
        await self.db_session.execute(query)

    async def update_confirmed_data_and_state(
        self, public_id: UUID, confirmed_data: ConfirmedData, state: ConversationState
    ) -> None:
        """
        Update both confirmed data and state for a conversation in a single operation.

        Args:
            public_id: The public ID of the conversation
            confirmed_data: The confirmed data to store
            state: The new conversation state

        Returns:
            None
        """
        logger.debug('Updating confirmed data and state for conversation ID: %s to %s', public_id, state)
        query = (
            update(QualConversation)
            .where(QualConversation.PublicId == public_id)
            .values(ConfirmedData=confirmed_data.to_json_string(), State=state)
        )
        await self.db_session.execute(query)

    async def get_confirmed_data(self, public_id: UUID) -> ConfirmedData:
        """
        Get the confirmed data for a conversation.

        Args:
            public_id: The public ID of the conversation

        Returns:
            ConfirmedData object (empty if no data exists)
        """
        query = select(QualConversation.ConfirmedData).where(QualConversation.PublicId == public_id)
        result = await self.db_session.execute(query)
        confirmed_data_json = result.scalar_one_or_none()
        return ConfirmedData.from_json_string(confirmed_data_json)

    async def get_dash_task_activity_id(self, public_id: UUID) -> int | None:
        """
        Get the dash task activity ID for a conversation.

        Args:
            public_id: The public ID of the conversation

        Returns:
            The dash task activity ID if found, None otherwise
        """
        query = select(QualConversation.DashActivityId).where(QualConversation.PublicId == public_id)
        result = await self.db_session.execute(query)
        return result.scalar_one_or_none()

    async def update_dash_activity_id(self, public_id: UUID, dash_activity_id: int) -> None:
        """
        Update the dash activity ID for a conversation.

        Args:
            public_id: The public ID of the conversation
            dash_activity_id: The dash activity ID to set

        Returns:
            None

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
        """
        logger.debug('Updating dash activity ID for conversation ID: %s to %s', public_id, dash_activity_id)
        if not await self.exists(public_id):
            raise EntityNotFoundError('Conversation', str(public_id))

        query = (
            update(QualConversation)
            .where(QualConversation.PublicId == public_id)
            .values(DashActivityId=dash_activity_id)
        )
        await self.db_session.execute(query)

    async def get_extra_data(self, public_id: UUID) -> QualConversation | None:
        """
        Get extra data for conversation by its public ID.

        Args:
            public_id: The public ID of the conversation

        Returns:
            The conversation itself, if found, None otherwise
        """
        logger.debug('Getting extended data for conversation ID: %s for debug', public_id)
        query = (
            select(QualConversation)
            .options(selectinload(QualConversation.ExtractedData))
            .where(QualConversation.PublicId == public_id)
        )
        result = await self.db_session.execute(query)
        result = result.scalar_one_or_none()
        if result:
            for extracted_data in result.ExtractedData:
                extracted_data.ConversationPublicId = result.PublicId
        return result

    async def update_qual_id(self, public_id: UUID, qual_id: str) -> None:
        """
        Update the QualId for a conversation.

        Args:
            public_id: The public ID of the conversation
            qual_id: The QualId to set

        Returns:
            None

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
        """
        logger.debug('Updating QualId for conversation ID: %s to %s', public_id, qual_id)
        if not await self.exists(public_id):
            raise EntityNotFoundError('Conversation', str(public_id))

        query = update(QualConversation).where(QualConversation.PublicId == public_id).values(QualId=qual_id)
        await self.db_session.execute(query)
