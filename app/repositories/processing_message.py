from typing import Sequence
from uuid import UUID

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import aliased

from models import QualConversationMessage, QualProcessingMessage
from schemas import ProcessingStatusUpdatePayload


__all__ = ['ProcessingMessageRepository']


class ProcessingMessageRepository:  # pragma: no cover  # Excluded from coverage check as related to durable_functions
    """
    A repository class to abstract database operations for file processing messages.
    """

    def __init__(self, db_session: AsyncSession):
        self.db_session = db_session

    async def create(self, data: ProcessingStatusUpdatePayload) -> QualProcessingMessage:
        """
        Create the processing status of a message.

        Args:
            message_id: The ID of the message
            status: The new status
            message: Optional message to include
            metadata: Optional metadata to include

        Returns:
            The created message data
        """
        message_alias = aliased(QualConversationMessage, name='msg')
        query = select(message_alias.Id).where(message_alias.PublicId == data.message_id)
        result = await self.db_session.execute(query)
        message_id = result.scalar_one()

        new_status = QualProcessingMessage(
            QualConversationMessageId=message_id,
            Status=data.status,
            Message=data.message,
            Metadata=data.metadata,
        )

        self.db_session.add(new_status)
        await self.db_session.flush()

        new_status.MessagePublicId = data.message_id

        return new_status

    async def get_message_processing_statuses(self, public_id: UUID) -> Sequence[str]:
        """
        Get all processing statuses for the message.

        Args:
            public_id: The public ID of the message

        Returns:
            List of processing statuses for the message
        """
        query = (
            select(QualProcessingMessage.Status)
            .join(
                QualConversationMessage, QualConversationMessage.Id == QualProcessingMessage.QualConversationMessageId
            )
            .where(QualConversationMessage.PublicId == public_id)
        )

        result = await self.db_session.execute(query)

        return result.scalars().all()

    async def get_corrupted_filenames_for_message(self, public_id: UUID) -> Sequence[str]:
        """
        Get all corrupted filenames for the message.

        Args:
            public_id: The public ID of the message

        Returns:
            List of corrupted filenames for the message
        """
        query = (
            select(QualProcessingMessage.Metadata)
            .join(
                QualConversationMessage, QualConversationMessage.Id == QualProcessingMessage.QualConversationMessageId
            )
            .where(QualConversationMessage.PublicId == public_id)
            .where(QualProcessingMessage.Status == 'DocumentIsCorrupted')
        )

        result = await self.db_session.execute(query)
        results = result.scalars().all()

        filenames = []
        for result in results:
            # only extract when we actually have a dict with a non-empty file_name
            if result and isinstance(result, dict) and 'file_name' in result:
                filename = result['file_name']
                if filename:
                    filenames.append(filename)
        return filenames
