import logging
from typing import Any

from config import settings
from core.http_client import CustomAsyncClient
from core.urls import url_join


__all__ = ['ServiceRepository']

logger = logging.getLogger(__name__)


class ServiceRepository:
    """Repository for Service API operations."""

    def __init__(self, http_client: CustomAsyncClient):
        """
        Initialize the Service Repository with an HTTP client.

        Args:
            http_client: The CustomAsyncClient to use for requests
        """
        self._http_client = http_client
        self._base_path = str(settings.services_api.base_url)

    async def list(self, token: str) -> list[dict[str, Any]]:
        """
        List all services.

        Returns:
            list[dict[str, Any]]: A list of services

        Raises:
            Exception: If an error occurs while listing services
        """
        url = url_join(self._base_path, 'services-all')
        headers = {'Authorization': f'Bearer {token}'}
        try:
            return (await self._http_client.get(url, headers=headers)).json()

        except Exception as e:
            logger.error('Error listing services: %s', e)
            raise e
